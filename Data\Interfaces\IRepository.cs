using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestionAssociations.Data.Interfaces
{
    /// <summary>
    /// Interface générique pour les repositories
    /// </summary>
    /// <typeparam name="T">Type de l'entité</typeparam>
    public interface IRepository<T> where T : class
    {
        /// <summary>
        /// Obtient une entité par son ID
        /// </summary>
        /// <param name="id">ID de l'entité</param>
        /// <returns>L'entité ou null si non trouvée</returns>
        Task<T> GetByIdAsync(int id);

        /// <summary>
        /// Obtient toutes les entités
        /// </summary>
        /// <returns>Liste de toutes les entités</returns>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// Obtient les entités avec pagination
        /// </summary>
        /// <param name="pageNumber">Numéro de page (commence à 1)</param>
        /// <param name="pageSize"><PERSON>lle de la page</param>
        /// <returns>Résultat paginé</returns>
        Task<PagedResult<T>> GetPagedAsync(int pageNumber, int pageSize);

        /// <summary>
        /// Crée une nouvelle entité
        /// </summary>
        /// <param name="entity">Entité à créer</param>
        /// <returns>ID de l'entité créée</returns>
        Task<int> CreateAsync(T entity);

        /// <summary>
        /// Met à jour une entité existante
        /// </summary>
        /// <param name="entity">Entité à mettre à jour</param>
        /// <returns>True si la mise à jour a réussi</returns>
        Task<bool> UpdateAsync(T entity);

        /// <summary>
        /// Supprime une entité par son ID
        /// </summary>
        /// <param name="id">ID de l'entité à supprimer</param>
        /// <returns>True si la suppression a réussi</returns>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// Vérifie si une entité existe
        /// </summary>
        /// <param name="id">ID de l'entité</param>
        /// <returns>True si l'entité existe</returns>
        Task<bool> ExistsAsync(int id);

        /// <summary>
        /// Compte le nombre total d'entités
        /// </summary>
        /// <returns>Nombre total d'entités</returns>
        Task<int> CountAsync();
    }

    /// <summary>
    /// Classe pour les résultats paginés
    /// </summary>
    /// <typeparam name="T">Type des éléments</typeparam>
    public class PagedResult<T>
    {
        public IEnumerable<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}
