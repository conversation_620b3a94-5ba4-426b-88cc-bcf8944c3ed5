using System;
using System.Drawing;
using System.Windows.Forms;
using GestionAssociations.Models;

namespace GestionAssociations.Forms.Association
{
    public partial class NouveauCommissaireForm : Form
    {
        private ComboBox cmbDateAssemblee;
        private TextBox txtNomPrenom;
        private TextBox txtCabinet;
        private ComboBox cmbFonction;
        private DateTimePicker dtpDateDebut;
        private DateTimePicker dtpDateFin;
        private Button btnValider;
        private Button btnAnnuler;
        private Button btnAjouterFonction;

        public CommissaireAuxComptes Commissaire { get; private set; }

        public NouveauCommissaireForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            LoadData();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "Nouveau Commissaire aux comptes";
            this.Size = new Size(500, 450);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Panel principal avec titre
            var titlePanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(500, 40),
                BackColor = Color.FromArgb(70, 70, 100)
            };

            var titleLabel = new Label
            {
                Text = "Nouveau Commissaire aux comptes",
                Location = new Point(15, 10),
                Size = new Size(300, 20),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            titlePanel.Controls.Add(titleLabel);

            // Panel de contenu
            var contentPanel = new Panel
            {
                Location = new Point(0, 40),
                Size = new Size(500, 370),
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Section Le commissaire
            var lblSectionCommissaire = new Label
            {
                Text = "Le commissaire",
                Location = new Point(20, 20),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // Date assemblée
            var lblDateAssemblee = new Label
            {
                Text = "Date assemblée",
                Location = new Point(20, 60),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            cmbDateAssemblee = new ComboBox
            {
                Location = new Point(150, 57),
                Size = new Size(200, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Nom Prénom
            var lblNomPrenom = new Label
            {
                Text = "Nom Prénom",
                Location = new Point(20, 100),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            txtNomPrenom = new TextBox
            {
                Location = new Point(150, 97),
                Size = new Size(300, 23)
            };

            // Cabinet
            var lblCabinet = new Label
            {
                Text = "Cabinet",
                Location = new Point(20, 140),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            txtCabinet = new TextBox
            {
                Location = new Point(150, 137),
                Size = new Size(300, 23)
            };

            // Section Son mandat
            var lblSectionMandat = new Label
            {
                Text = "Son mandat",
                Location = new Point(20, 180),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // Fonction
            var lblFonction = new Label
            {
                Text = "Fonction",
                Location = new Point(20, 220),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            cmbFonction = new ComboBox
            {
                Location = new Point(150, 217),
                Size = new Size(250, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            btnAjouterFonction = new Button
            {
                Text = "+",
                Location = new Point(410, 217),
                Size = new Size(30, 23),
                BackColor = Color.FromArgb(255, 165, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            // Date début
            var lblDateDebut = new Label
            {
                Text = "Date début",
                Location = new Point(20, 260),
                Size = new Size(80, 23),
                Font = new Font("Segoe UI", 9F)
            };

            dtpDateDebut = new DateTimePicker
            {
                Location = new Point(110, 257),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };

            // Au
            var lblAu = new Label
            {
                Text = "Au",
                Location = new Point(250, 260),
                Size = new Size(30, 23),
                Font = new Font("Segoe UI", 9F)
            };

            // Date fin
            dtpDateFin = new DateTimePicker
            {
                Location = new Point(290, 257),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today.AddYears(1)
            };

            // Boutons
            btnValider = new Button
            {
                Text = "Valider",
                Location = new Point(250, 320),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(70, 70, 100),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            btnAnnuler = new Button
            {
                Text = "Annuler",
                Location = new Point(360, 320),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(150, 150, 150),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            // Événements
            btnValider.Click += BtnValider_Click;
            btnAnnuler.Click += BtnAnnuler_Click;
            btnAjouterFonction.Click += BtnAjouterFonction_Click;

            // Ajouter les contrôles au panel de contenu
            contentPanel.Controls.AddRange(new Control[]
            {
                lblSectionCommissaire,
                lblDateAssemblee, cmbDateAssemblee,
                lblNomPrenom, txtNomPrenom,
                lblCabinet, txtCabinet,
                lblSectionMandat,
                lblFonction, cmbFonction, btnAjouterFonction,
                lblDateDebut, dtpDateDebut, lblAu, dtpDateFin,
                btnValider, btnAnnuler
            });

            // Ajouter les panels au formulaire
            this.Controls.AddRange(new Control[] { titlePanel, contentPanel });
        }

        private void LoadData()
        {
            // Charger les dates d'assemblée
            cmbDateAssemblee.Items.Add("08/01/2025");
            cmbDateAssemblee.SelectedIndex = 0;

            // Charger les fonctions
            cmbFonction.Items.AddRange(new string[]
            {
                "Vice-Président",
                "Président",
                "Trésorier",
                "Secrétaire",
                "Commissaire aux comptes"
            });
        }

        private void BtnValider_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                Commissaire = new CommissaireAuxComptes
                {
                    NomPrenom = txtNomPrenom.Text.Trim(),
                    Cabinet = txtCabinet.Text.Trim(),
                    Fonction = cmbFonction.Text,
                    DateDebut = dtpDateDebut.Value,
                    DateFin = dtpDateFin.Value,
                    EstActif = true,
                    DateCreation = DateTime.Now
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnAnnuler_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnAjouterFonction_Click(object sender, EventArgs e)
        {
            // TODO: Ouvrir un formulaire pour ajouter une nouvelle fonction
            MessageBox.Show("Ajouter nouvelle fonction - À implémenter", "Information");
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtNomPrenom.Text))
            {
                MessageBox.Show("Le nom et prénom sont obligatoires.", "Validation", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNomPrenom.Focus();
                return false;
            }

            if (cmbFonction.SelectedIndex == -1)
            {
                MessageBox.Show("Veuillez sélectionner une fonction.", "Validation", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbFonction.Focus();
                return false;
            }

            if (dtpDateFin.Value <= dtpDateDebut.Value)
            {
                MessageBox.Show("La date de fin doit être postérieure à la date de début.", "Validation", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dtpDateFin.Focus();
                return false;
            }

            return true;
        }
    }
}
