using System;
using System.Drawing;
using System.Windows.Forms;
using GestionAssociations.Models;

namespace GestionAssociations.Forms.Adherents
{
    public partial class ListeAdherentsForm : Form
    {
        private ComboBox cmbPeriode;
        private ComboBox cmbPole;
        private ComboBox cmbCommune;
        private TextBox txtRecherche;
        private DataGridView dgvAdherents;
        private Button btnNouvel;
        private Button btnModifier;
        private Button btnSupprimer;
        private Button btnImprimer;
        private Button btnExporter;
        private Label lblNombreAdherents;

        public ListeAdherentsForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            LoadData();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "Liste des adhérents";
            this.Size = new Size(1400, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.WindowState = FormWindowState.Maximized;

            // Panel de filtres
            var filterPanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(1400, 60),
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Période
            var lblPeriode = new Label
            {
                Text = "Période",
                Location = new Point(10, 20),
                Size = new Size(60, 20),
                Font = new Font("Segoe UI", 9F)
            };

            cmbPeriode = new ComboBox
            {
                Location = new Point(80, 17),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Pôle
            var lblPole = new Label
            {
                Text = "Pôle",
                Location = new Point(220, 20),
                Size = new Size(40, 20),
                Font = new Font("Segoe UI", 9F)
            };

            cmbPole = new ComboBox
            {
                Location = new Point(270, 17),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Commune
            var lblCommune = new Label
            {
                Text = "Commune",
                Location = new Point(440, 20),
                Size = new Size(70, 20),
                Font = new Font("Segoe UI", 9F)
            };

            cmbCommune = new ComboBox
            {
                Location = new Point(520, 17),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Recherche
            var lblRecherche = new Label
            {
                Text = "Recherche",
                Location = new Point(690, 20),
                Size = new Size(70, 20),
                Font = new Font("Segoe UI", 9F)
            };

            txtRecherche = new TextBox
            {
                Location = new Point(770, 17),
                Size = new Size(200, 23),
                PlaceholderText = "Nom, prénom, email..."
            };

            // Nombre d'adhérents
            lblNombreAdherents = new Label
            {
                Text = "18 adhérents",
                Location = new Point(1000, 20),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                ForeColor = Color.FromArgb(70, 70, 100)
            };

            filterPanel.Controls.AddRange(new Control[]
            {
                lblPeriode, cmbPeriode, lblPole, cmbPole,
                lblCommune, cmbCommune, lblRecherche, txtRecherche,
                lblNombreAdherents
            });

            // DataGridView
            dgvAdherents = new DataGridView
            {
                Location = new Point(0, 60),
                Size = new Size(1400, 650),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                RowHeadersVisible = false
            };

            // Colonnes
            dgvAdherents.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "NumeroAdherent", HeaderText = "N°", Width = 60 },
                new DataGridViewTextBoxColumn { Name = "Nom", HeaderText = "Nom", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Prenom", HeaderText = "Prénom", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "DateNaissance", HeaderText = "Date naissance", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "TypeAdherent", HeaderText = "Type d'adhérent", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Activite", HeaderText = "Activité", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Age", HeaderText = "Age", Width = 50 },
                new DataGridViewTextBoxColumn { Name = "Adresse", HeaderText = "Adresse", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "Ville", HeaderText = "Ville", Width = 120 },
                new DataGridViewTextBoxColumn { Name = "Telephone", HeaderText = "Téléphone", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Email", HeaderText = "E-mail", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "Commentaires", HeaderText = "Commentaires", Width = 150 }
            });

            // Styliser l'en-tête
            dgvAdherents.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(70, 70, 100);
            dgvAdherents.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvAdherents.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            dgvAdherents.EnableHeadersVisualStyles = false;
            dgvAdherents.ColumnHeadersHeight = 30;

            // Styliser les lignes alternées
            dgvAdherents.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 248, 248);
            dgvAdherents.DefaultCellStyle.SelectionBackColor = Color.FromArgb(70, 70, 100);
            dgvAdherents.DefaultCellStyle.SelectionForeColor = Color.White;

            // Panel de boutons
            var buttonPanel = new Panel
            {
                Location = new Point(0, 710),
                Size = new Size(1400, 50),
                BackColor = Color.FromArgb(240, 240, 240),
                BorderStyle = BorderStyle.FixedSingle
            };

            btnNouvel = CreateButton("Nouvel adhérent", new Point(10, 10), Color.FromArgb(70, 70, 100));
            btnModifier = CreateButton("Modifier", new Point(140, 10), Color.FromArgb(70, 70, 100));
            btnSupprimer = CreateButton("Supprimer", new Point(250, 10), Color.FromArgb(200, 50, 50));
            btnImprimer = CreateButton("Imprimer", new Point(360, 10), Color.FromArgb(70, 70, 100));
            btnExporter = CreateButton("Exporter", new Point(470, 10), Color.FromArgb(70, 70, 100));

            // Événements
            btnNouvel.Click += BtnNouvel_Click;
            btnModifier.Click += BtnModifier_Click;
            btnSupprimer.Click += BtnSupprimer_Click;
            btnImprimer.Click += BtnImprimer_Click;
            btnExporter.Click += BtnExporter_Click;
            txtRecherche.TextChanged += TxtRecherche_TextChanged;
            cmbPeriode.SelectedIndexChanged += Filter_Changed;
            cmbPole.SelectedIndexChanged += Filter_Changed;
            cmbCommune.SelectedIndexChanged += Filter_Changed;
            dgvAdherents.CellDoubleClick += DgvAdherents_CellDoubleClick;

            buttonPanel.Controls.AddRange(new Control[]
            {
                btnNouvel, btnModifier, btnSupprimer, btnImprimer, btnExporter
            });

            // Ajouter tous les contrôles au formulaire
            this.Controls.AddRange(new Control[]
            {
                filterPanel, dgvAdherents, buttonPanel
            });
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(120, 30),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
        }

        private void LoadData()
        {
            // Charger les filtres
            cmbPeriode.Items.AddRange(new string[] { "2024-2025", "2023-2024", "2022-2023" });
            cmbPeriode.SelectedIndex = 0;

            cmbPole.Items.AddRange(new string[] { "Tous", "Formation Musicale", "Instrument", "Pratique Collective" });
            cmbPole.SelectedIndex = 0;

            cmbCommune.Items.AddRange(new string[] { "Toutes", "Paris", "Lyon", "Marseille" });
            cmbCommune.SelectedIndex = 0;

            // Charger les adhérents (données de démonstration)
            LoadAdherents();
        }

        private void LoadAdherents()
        {
            // Données de démonstration basées sur les captures d'écran
            var adherents = new[]
            {
                new { NumeroAdherent = "34", Nom = "BELL", Prenom = "Virginie", DateNaissance = "06/03/2016", TypeAdherent = "JUDO SENIOR", Activite = "", Age = "8", Adresse = "", Ville = "", Telephone = "", Email = "SALLE PAPIN", Commentaires = "" },
                new { NumeroAdherent = "35", Nom = "BENCOUME", Prenom = "Virginie", DateNaissance = "", TypeAdherent = "", Activite = "", Age = "", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "" },
                new { NumeroAdherent = "36", Nom = "BENCOUME", Prenom = "Fabienne", DateNaissance = "", TypeAdherent = "", Activite = "", Age = "", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "" },
                new { NumeroAdherent = "37", Nom = "DURAND", Prenom = "Pierre", DateNaissance = "15/06/1985", TypeAdherent = "JUDO SENIOR", Activite = "24/12/2019", Age = "39", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "LILLE SUR LA SORGUE" },
                new { NumeroAdherent = "47", Nom = "GARCIA", Prenom = "Philippe", DateNaissance = "10/01/1940", TypeAdherent = "AU 31/50", Activite = "", Age = "84", Adresse = "06/12/14/7", Telephone = "<EMAIL>", Email = "", Commentaires = "SALLE PAPIN" },
                new { NumeroAdherent = "50", Nom = "Gaelle", Prenom = "Alain", DateNaissance = "13/04/2004", TypeAdherent = "", Activite = "", Age = "20", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "" },
                new { NumeroAdherent = "108", Nom = "JOUELLIT", Prenom = "JOCELYNE", DateNaissance = "20/05/1970", TypeAdherent = "2022", Activite = "33", Age = "54", Adresse = "", Ville = "", Telephone = "<EMAIL>", Email = "", Commentaires = "SOYEUX" },
                new { NumeroAdherent = "44", Nom = "ROMAN", Prenom = "MICHEL", DateNaissance = "11/01/1963", TypeAdherent = "TUBA", Activite = "", Age = "61", Adresse = "", Ville = "", Telephone = "<EMAIL>", Email = "", Commentaires = "LILLE SUR LA SORGUE" },
                new { NumeroAdherent = "45", Nom = "ROMAN", Prenom = "MARIE-LILIANE", DateNaissance = "08/01/1978", TypeAdherent = "", Activite = "34", Age = "46", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "SOYEUX" },
                new { NumeroAdherent = "42", Nom = "TESTIT", Prenom = "TESTIT", DateNaissance = "", TypeAdherent = "ORGUES", Activite = "", Age = "", Adresse = "", Ville = "", Telephone = "<EMAIL>", Email = "", Commentaires = "PERTHUISOT" },
                new { NumeroAdherent = "39", Nom = "TESTIZ", Prenom = "TESTIZ", DateNaissance = "01/01/1978", TypeAdherent = "JUDO ENFANTS", Activite = "", Age = "46", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "99999 H" },
                new { NumeroAdherent = "40", Nom = "TESTIZ", Prenom = "TESTIZ", DateNaissance = "", TypeAdherent = "JUDO ENFANTS", Activite = "", Age = "", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "ORLEANS" },
                new { NumeroAdherent = "41", Nom = "TOTO", Prenom = "JEAN", DateNaissance = "03/01/2006", TypeAdherent = "Adulte", Activite = "25", Age = "18", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "" },
                new { NumeroAdherent = "46", Nom = "TOTO", Prenom = "JEAN", DateNaissance = "20/06/1948", TypeAdherent = "Adulte", Activite = "77", Age = "76", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "" },
                new { NumeroAdherent = "107", Nom = "TOTO", Prenom = "Isabelle", DateNaissance = "27/08/1965", TypeAdherent = "2022", Activite = "70", Age = "59", Adresse = "06/42/24/75", Telephone = "<EMAIL>", Email = "", Commentaires = "AVIGNON" },
                new { NumeroAdherent = "45", Nom = "TOTO", Prenom = "Claude", DateNaissance = "", TypeAdherent = "Adhérent", Activite = "", Age = "", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "LES HOUCHES" },
                new { NumeroAdherent = "107", Nom = "TOTO", Prenom = "Isabelle", DateNaissance = "27/08/1965", TypeAdherent = "2022", Activite = "70", Age = "59", Adresse = "06/42/24/75", Telephone = "<EMAIL>", Email = "", Commentaires = "AVIGNON" },
                new { NumeroAdherent = "45", Nom = "TOTO", Prenom = "Claude", DateNaissance = "", TypeAdherent = "Adhérent", Activite = "", Age = "", Adresse = "", Ville = "", Telephone = "", Email = "", Commentaires = "LES HOUCHES" }
            };

            dgvAdherents.DataSource = adherents;
            lblNombreAdherents.Text = $"{adherents.Length} adhérents";
        }

        private void BtnNouvel_Click(object sender, EventArgs e)
        {
            var form = new NouvelAdherentForm();
            if (form.ShowDialog() == DialogResult.OK)
            {
                LoadAdherents();
            }
        }

        private void BtnModifier_Click(object sender, EventArgs e)
        {
            if (dgvAdherents.SelectedRows.Count == 0)
            {
                MessageBox.Show("Veuillez sélectionner un adhérent à modifier.", "Information");
                return;
            }

            // TODO: Récupérer l'adhérent sélectionné et ouvrir le formulaire de modification
            var form = new NouvelAdherentForm(); // Passer l'adhérent en paramètre
            if (form.ShowDialog() == DialogResult.OK)
            {
                LoadAdherents();
            }
        }

        private void BtnSupprimer_Click(object sender, EventArgs e)
        {
            if (dgvAdherents.SelectedRows.Count == 0)
            {
                MessageBox.Show("Veuillez sélectionner un adhérent à supprimer.", "Information");
                return;
            }

            var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer cet adhérent ?", 
                "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // TODO: Implémenter la suppression
                MessageBox.Show("Adhérent supprimé avec succès!", "Succès");
                LoadAdherents();
            }
        }

        private void BtnImprimer_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter l'impression
            MessageBox.Show("Impression - À implémenter", "Information");
        }

        private void BtnExporter_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter l'export
            MessageBox.Show("Export - À implémenter", "Information");
        }

        private void TxtRecherche_TextChanged(object sender, EventArgs e)
        {
            // TODO: Implémenter la recherche
            ApplyFilters();
        }

        private void Filter_Changed(object sender, EventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            // TODO: Implémenter les filtres
            LoadAdherents();
        }

        private void DgvAdherents_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                BtnModifier_Click(sender, e);
            }
        }
    }
}
