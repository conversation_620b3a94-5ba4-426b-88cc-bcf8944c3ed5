using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentValidation;
using FluentValidation.Results;
using GestionAssociations.Core.Validators;
using GestionAssociations.Models;

namespace GestionAssociations.Core.Services
{
    /// <summary>
    /// Service de validation centralisé
    /// </summary>
    public class ValidationService : IValidationService
    {
        private readonly Dictionary<Type, IValidator> _validators;

        public ValidationService()
        {
            _validators = new Dictionary<Type, IValidator>
            {
                { typeof(Adherent), new AdherentValidator() },
                // Ajouter d'autres validateurs ici
            };
        }

        /// <summary>
        /// Valide un objet
        /// </summary>
        /// <typeparam name="T">Type de l'objet</typeparam>
        /// <param name="entity">Objet à valider</param>
        /// <returns>Résultat de la validation</returns>
        public ValidationResult Validate<T>(T entity) where T : class
        {
            if (entity == null)
            {
                return new ValidationResult(new[]
                {
                    new ValidationFailure("Entity", "L'objet à valider ne peut pas être null")
                });
            }

            var validator = GetValidator<T>();
            if (validator == null)
            {
                // Pas de validateur défini, considérer comme valide
                return new ValidationResult();
            }

            return validator.Validate(entity);
        }

        /// <summary>
        /// Valide un objet de manière asynchrone
        /// </summary>
        /// <typeparam name="T">Type de l'objet</typeparam>
        /// <param name="entity">Objet à valider</param>
        /// <returns>Résultat de la validation</returns>
        public async Task<ValidationResult> ValidateAsync<T>(T entity) where T : class
        {
            if (entity == null)
            {
                return new ValidationResult(new[]
                {
                    new ValidationFailure("Entity", "L'objet à valider ne peut pas être null")
                });
            }

            var validator = GetValidator<T>();
            if (validator == null)
            {
                return new ValidationResult();
            }

            return await validator.ValidateAsync(entity);
        }

        /// <summary>
        /// Valide un objet et lève une exception si la validation échoue
        /// </summary>
        /// <typeparam name="T">Type de l'objet</typeparam>
        /// <param name="entity">Objet à valider</param>
        /// <exception cref="ValidationException">Levée si la validation échoue</exception>
        public void ValidateAndThrow<T>(T entity) where T : class
        {
            var result = Validate(entity);
            if (!result.IsValid)
            {
                throw new ValidationException(result.Errors);
            }
        }

        /// <summary>
        /// Valide un objet de manière asynchrone et lève une exception si la validation échoue
        /// </summary>
        /// <typeparam name="T">Type de l'objet</typeparam>
        /// <param name="entity">Objet à valider</param>
        /// <exception cref="ValidationException">Levée si la validation échoue</exception>
        public async Task ValidateAndThrowAsync<T>(T entity) where T : class
        {
            var result = await ValidateAsync(entity);
            if (!result.IsValid)
            {
                throw new ValidationException(result.Errors);
            }
        }

        /// <summary>
        /// Vérifie si un objet est valide
        /// </summary>
        /// <typeparam name="T">Type de l'objet</typeparam>
        /// <param name="entity">Objet à valider</param>
        /// <returns>True si l'objet est valide</returns>
        public bool IsValid<T>(T entity) where T : class
        {
            var result = Validate(entity);
            return result.IsValid;
        }

        /// <summary>
        /// Vérifie si un objet est valide de manière asynchrone
        /// </summary>
        /// <typeparam name="T">Type de l'objet</typeparam>
        /// <param name="entity">Objet à valider</param>
        /// <returns>True si l'objet est valide</returns>
        public async Task<bool> IsValidAsync<T>(T entity) where T : class
        {
            var result = await ValidateAsync(entity);
            return result.IsValid;
        }

        /// <summary>
        /// Obtient les erreurs de validation pour un objet
        /// </summary>
        /// <typeparam name="T">Type de l'objet</typeparam>
        /// <param name="entity">Objet à valider</param>
        /// <returns>Liste des erreurs de validation</returns>
        public IEnumerable<string> GetValidationErrors<T>(T entity) where T : class
        {
            var result = Validate(entity);
            return result.Errors.Select(e => e.ErrorMessage);
        }

        /// <summary>
        /// Obtient les erreurs de validation pour un objet de manière asynchrone
        /// </summary>
        /// <typeparam name="T">Type de l'objet</typeparam>
        /// <param name="entity">Objet à valider</param>
        /// <returns>Liste des erreurs de validation</returns>
        public async Task<IEnumerable<string>> GetValidationErrorsAsync<T>(T entity) where T : class
        {
            var result = await ValidateAsync(entity);
            return result.Errors.Select(e => e.ErrorMessage);
        }

        /// <summary>
        /// Valide une propriété spécifique d'un objet
        /// </summary>
        /// <typeparam name="T">Type de l'objet</typeparam>
        /// <param name="entity">Objet à valider</param>
        /// <param name="propertyName">Nom de la propriété</param>
        /// <returns>Résultat de la validation</returns>
        public ValidationResult ValidateProperty<T>(T entity, string propertyName) where T : class
        {
            var validator = GetValidator<T>();
            if (validator == null)
            {
                return new ValidationResult();
            }

            var context = new ValidationContext<T>(entity, new PropertyChain(), new MemberNameValidatorSelector(new[] { propertyName }));
            return validator.Validate(context);
        }

        /// <summary>
        /// Obtient le validateur pour un type donné
        /// </summary>
        /// <typeparam name="T">Type pour lequel obtenir le validateur</typeparam>
        /// <returns>Validateur ou null si aucun validateur n'est défini</returns>
        private IValidator<T> GetValidator<T>() where T : class
        {
            var type = typeof(T);
            if (_validators.TryGetValue(type, out var validator))
            {
                return validator as IValidator<T>;
            }
            return null;
        }

        /// <summary>
        /// Ajoute un validateur pour un type donné
        /// </summary>
        /// <typeparam name="T">Type pour lequel ajouter le validateur</typeparam>
        /// <param name="validator">Validateur à ajouter</param>
        public void AddValidator<T>(IValidator<T> validator) where T : class
        {
            _validators[typeof(T)] = validator;
        }

        /// <summary>
        /// Supprime le validateur pour un type donné
        /// </summary>
        /// <typeparam name="T">Type pour lequel supprimer le validateur</typeparam>
        public void RemoveValidator<T>() where T : class
        {
            _validators.Remove(typeof(T));
        }

        /// <summary>
        /// Vérifie si un validateur existe pour un type donné
        /// </summary>
        /// <typeparam name="T">Type à vérifier</typeparam>
        /// <returns>True si un validateur existe</returns>
        public bool HasValidator<T>() where T : class
        {
            return _validators.ContainsKey(typeof(T));
        }
    }

    /// <summary>
    /// Interface pour le service de validation
    /// </summary>
    public interface IValidationService
    {
        ValidationResult Validate<T>(T entity) where T : class;
        Task<ValidationResult> ValidateAsync<T>(T entity) where T : class;
        void ValidateAndThrow<T>(T entity) where T : class;
        Task ValidateAndThrowAsync<T>(T entity) where T : class;
        bool IsValid<T>(T entity) where T : class;
        Task<bool> IsValidAsync<T>(T entity) where T : class;
        IEnumerable<string> GetValidationErrors<T>(T entity) where T : class;
        Task<IEnumerable<string>> GetValidationErrorsAsync<T>(T entity) where T : class;
        ValidationResult ValidateProperty<T>(T entity, string propertyName) where T : class;
        void AddValidator<T>(IValidator<T> validator) where T : class;
        void RemoveValidator<T>() where T : class;
        bool HasValidator<T>() where T : class;
    }
}
