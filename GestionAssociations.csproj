<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{12345678-1234-5678-9ABC-123456789ABC}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>GestionAssociations</RootNamespace>
    <AssemblyName>GestionAssociations</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Configuration" />
    <Reference Include="Dapper, Version=2.1.35.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\Dapper.2.1.35\lib\net461\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SqlClient, Version=4.6.1.6, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Data.SqlClient.4.8.6\lib\net461\System.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Association\DescriptionAssociationForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Association\DescriptionAssociationForm.Designer.cs">
      <DependentUpon>DescriptionAssociationForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Association\MembresBureauForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Association\MembresBureauForm.Designer.cs">
      <DependentUpon>MembresBureauForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Association\NouvelleAssembleeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Association\NouvelleAssembleeForm.Designer.cs">
      <DependentUpon>NouvelleAssembleeForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Association\NouveauCommissaireForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Association\NouveauCommissaireForm.Designer.cs">
      <DependentUpon>NouveauCommissaireForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Association\NouveauMembreBureauForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Association\NouveauMembreBureauForm.Designer.cs">
      <DependentUpon>NouveauMembreBureauForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Association\GestionUtilisateursForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Association\GestionUtilisateursForm.Designer.cs">
      <DependentUpon>GestionUtilisateursForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Adherents\ListeAdherentsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Adherents\ListeAdherentsForm.Designer.cs">
      <DependentUpon>ListeAdherentsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Adherents\NouvelAdherentForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Adherents\NouvelAdherentForm.Designer.cs">
      <DependentUpon>NouvelAdherentForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Models\Association.cs" />
    <Compile Include="Models\Adherent.cs" />
    <Compile Include="Models\Cotisation.cs" />
    <Compile Include="Models\Pole.cs" />
    <Compile Include="Models\Bureau.cs" />
    <Compile Include="Data\DatabaseConnection.cs" />
    <Compile Include="Data\Repositories\IRepository.cs" />
    <Compile Include="Data\Repositories\BaseRepository.cs" />
    <Compile Include="Data\Repositories\AssociationRepository.cs" />
    <Compile Include="Services\AssociationService.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Forms\MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Data\" />
    <Folder Include="Forms\Association\" />
    <Folder Include="Forms\Adherents\" />
    <Folder Include="Forms\Comptabilite\" />
    <Folder Include="Forms\Organiser\" />
    <Folder Include="Forms\Secretariat\" />
    <Folder Include="Forms\Materiels\" />
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Utils\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
