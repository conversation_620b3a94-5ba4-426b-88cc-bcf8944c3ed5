using System;
using System.Drawing;
using System.Windows.Forms;
using GestionAssociations.Models;

namespace GestionAssociations.Forms.Association
{
    public partial class GestionUtilisateursForm : Form
    {
        private Label lblDescription;
        private DataGridView dgvUtilisateurs;
        private ComboBox cmbGestionnairePrincipal;
        private ComboBox cmbGestionnaireSecondaire;
        private Button btnValider;
        private Button btnNouveau;
        private Button btnModifier;
        private Button btnSupprimer;

        public GestionUtilisateursForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            LoadData();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "Gestion des utilisateurs";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;

            // Description
            lblDescription = new Label
            {
                Text = "Gérez ici les utilisateurs et leurs niveaux d'accès. Le précédent système de double authentification a été supprimé et tout la gestion des utilisateurs se fait désormais dans cet écran.\nN'hésitez pas à envoyer un message à <EMAIL> en cas de besoin.",
                Location = new Point(20, 20),
                Size = new Size(1150, 60),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.FromArgb(100, 100, 100)
            };

            // DataGridView pour les utilisateurs
            dgvUtilisateurs = new DataGridView
            {
                Location = new Point(20, 90),
                Size = new Size(900, 450),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Colonnes pour les utilisateurs
            dgvUtilisateurs.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Pole", HeaderText = "Pôle", DataPropertyName = "Pole", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Identifiant", HeaderText = "Identifiant", DataPropertyName = "Identifiant", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Nom", HeaderText = "Nom", DataPropertyName = "Nom", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Prenom", HeaderText = "Prénom", DataPropertyName = "Prenom", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Email", HeaderText = "Email", DataPropertyName = "Email", Width = 250 }
            });

            // Styliser l'en-tête
            dgvUtilisateurs.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(70, 70, 100);
            dgvUtilisateurs.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUtilisateurs.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            dgvUtilisateurs.EnableHeadersVisualStyles = false;

            // Styliser les lignes alternées
            dgvUtilisateurs.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(245, 245, 245);

            // Panel pour la gestion du compte client
            var panelGestionCompte = new Panel
            {
                Location = new Point(940, 90),
                Size = new Size(240, 200),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(250, 250, 250)
            };

            var lblTitreGestion = new Label
            {
                Text = "Gestionnaire(s) du compte client\n(abonnement, commandes, ...)",
                Location = new Point(10, 10),
                Size = new Size(220, 40),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold),
                TextAlign = ContentAlignment.TopCenter
            };

            var lblPrincipal = new Label
            {
                Text = "Principal",
                Location = new Point(10, 60),
                Size = new Size(80, 23),
                Font = new Font("Segoe UI", 9F)
            };

            cmbGestionnairePrincipal = new ComboBox
            {
                Location = new Point(10, 85),
                Size = new Size(220, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            var lblSecondaire = new Label
            {
                Text = "Secondaire",
                Location = new Point(10, 120),
                Size = new Size(80, 23),
                Font = new Font("Segoe UI", 9F)
            };

            cmbGestionnaireSecondaire = new ComboBox
            {
                Location = new Point(10, 145),
                Size = new Size(220, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            btnValider = new Button
            {
                Text = "Valider",
                Location = new Point(80, 180),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(70, 70, 100),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            panelGestionCompte.Controls.AddRange(new Control[]
            {
                lblTitreGestion, lblPrincipal, cmbGestionnairePrincipal,
                lblSecondaire, cmbGestionnaireSecondaire, btnValider
            });

            // Boutons d'action
            var buttonPanel = new Panel
            {
                Location = new Point(20, 550),
                Size = new Size(900, 50),
                BackColor = Color.FromArgb(240, 240, 240)
            };

            btnNouveau = new Button
            {
                Text = "Nouveau",
                Location = new Point(10, 10),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(70, 70, 100),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            btnModifier = new Button
            {
                Text = "Modifier",
                Location = new Point(120, 10),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(70, 70, 100),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            btnSupprimer = new Button
            {
                Text = "Supprimer",
                Location = new Point(230, 10),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(200, 50, 50),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            // Événements
            btnNouveau.Click += BtnNouveau_Click;
            btnModifier.Click += BtnModifier_Click;
            btnSupprimer.Click += BtnSupprimer_Click;
            btnValider.Click += BtnValider_Click;

            buttonPanel.Controls.AddRange(new Control[] { btnNouveau, btnModifier, btnSupprimer });

            // Ajouter tous les contrôles au formulaire
            this.Controls.AddRange(new Control[]
            {
                lblDescription, dgvUtilisateurs, panelGestionCompte, buttonPanel
            });
        }

        private void LoadData()
        {
            // Charger les données des utilisateurs (simulation)
            var utilisateurs = new[]
            {
                new { Pole = "DEMO", Identifiant = "DEMO000136730", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO000234940", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO000855400", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO001143810", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO001324520", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO002958380", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO003002790", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO005307370", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO005529840", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO010450620", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO011107610", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO011118650", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO011727430", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO012924020", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO014152840", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO015834860", Nom = "", Prenom = "", Email = "" },
                new { Pole = "", Identifiant = "DEMO023113750", Nom = "", Prenom = "", Email = "" }
            };

            dgvUtilisateurs.DataSource = utilisateurs;

            // Charger les gestionnaires
            cmbGestionnairePrincipal.Items.Add("DEMO");
            cmbGestionnairePrincipal.SelectedIndex = 0;

            cmbGestionnaireSecondaire.Items.Add("DEMO");
        }

        private void BtnNouveau_Click(object sender, EventArgs e)
        {
            // TODO: Ouvrir le formulaire de création d'utilisateur
            MessageBox.Show("Nouveau utilisateur - À implémenter", "Information");
        }

        private void BtnModifier_Click(object sender, EventArgs e)
        {
            if (dgvUtilisateurs.SelectedRows.Count == 0)
            {
                MessageBox.Show("Veuillez sélectionner un utilisateur à modifier.", "Information");
                return;
            }

            // TODO: Ouvrir le formulaire de modification d'utilisateur
            MessageBox.Show("Modifier utilisateur - À implémenter", "Information");
        }

        private void BtnSupprimer_Click(object sender, EventArgs e)
        {
            if (dgvUtilisateurs.SelectedRows.Count == 0)
            {
                MessageBox.Show("Veuillez sélectionner un utilisateur à supprimer.", "Information");
                return;
            }

            var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer cet utilisateur ?", 
                "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // TODO: Implémenter la suppression
                MessageBox.Show("Utilisateur supprimé avec succès!", "Succès");
                LoadData(); // Recharger les données
            }
        }

        private void BtnValider_Click(object sender, EventArgs e)
        {
            // TODO: Sauvegarder les gestionnaires sélectionnés
            MessageBox.Show("Gestionnaires mis à jour avec succès!", "Succès");
        }
    }
}
