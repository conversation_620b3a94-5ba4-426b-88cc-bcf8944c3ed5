using System;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;
using GestionAssociations.Models;
using GestionAssociations.Services;

namespace GestionAssociations.Forms.Association
{
    public partial class DescriptionAssociationForm : Form
    {
        private readonly IAssociationService _associationService;
        private Models.Association _association;
        private President _president;
        private Tresorier _tresorier;
        private Secretaire _secretaire;

        private TabControl tabControl;
        private TabPage tabCoordonnees;
        private TabPage tabBut;
        private TabPage tabAdministration;
        private TabPage tabPresident;
        private TabPage tabTresorier;
        private TabPage tabSecretaire;
        private TabPage tabLogo;

        public DescriptionAssociationForm()
        {
            _associationService = new AssociationService();
            InitializeComponent();
            InitializeCustomComponents();
            LoadDataAsync();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "Description association";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;

            // Créer le TabControl
            tabControl = new TabControl();
            tabControl.Dock = DockStyle.Fill;
            tabControl.Font = new Font("Segoe UI", 9F);

            // Onglet Coordonnées
            tabCoordonnees = new TabPage("Coordonnées");
            CreateCoordonneesTab();

            // Onglet But de l'association
            tabBut = new TabPage("But de l'association");
            CreateButTab();

            // Onglet Administration
            tabAdministration = new TabPage("Administration");
            CreateAdministrationTab();

            // Onglet Président
            tabPresident = new TabPage("Président");
            CreatePresidentTab();

            // Onglet Trésorier
            tabTresorier = new TabPage("Trésorier");
            CreateTresorierTab();

            // Onglet Secrétaire
            tabSecretaire = new TabPage("Secrétaire");
            CreateSecretaireTab();

            // Onglet Logo
            tabLogo = new TabPage("Logo");
            CreateLogoTab();

            tabControl.TabPages.AddRange(new TabPage[] {
                tabCoordonnees, tabBut, tabAdministration, 
                tabPresident, tabTresorier, tabSecretaire, tabLogo
            });

            this.Controls.Add(tabControl);
        }

        private void CreateCoordonneesTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // Nom de l'association
            var lblNom = new Label { Text = "Nom de l'association", Location = new Point(20, 30), Size = new Size(150, 23) };
            var txtNom = new TextBox { Name = "txtNom", Location = new Point(180, 27), Size = new Size(300, 23) };

            // Suite du nom
            var lblSuite = new Label { Text = "Suite du nom", Location = new Point(20, 60), Size = new Size(150, 23) };
            var txtSuite = new TextBox { Name = "txtSuite", Location = new Point(180, 57), Size = new Size(300, 23) };

            // Complément d'adresse
            var lblComplement = new Label { Text = "Complément d'adresse", Location = new Point(20, 90), Size = new Size(150, 23) };
            var txtComplement = new TextBox { Name = "txtComplement", Location = new Point(180, 87), Size = new Size(300, 23) };

            // N° et nom de la voie
            var lblVoie = new Label { Text = "N° et nom de la voie", Location = new Point(20, 120), Size = new Size(150, 23) };
            var txtVoie = new TextBox { Name = "txtVoie", Location = new Point(180, 117), Size = new Size(300, 23) };

            // BP ou Lieu-Dit
            var lblBp = new Label { Text = "BP ou Lieu-Dit", Location = new Point(20, 150), Size = new Size(150, 23) };
            var txtBp = new TextBox { Name = "txtBp", Location = new Point(180, 147), Size = new Size(300, 23) };

            // CP / Commune
            var lblCp = new Label { Text = "CP / Commune", Location = new Point(20, 180), Size = new Size(150, 23) };
            var txtCp = new TextBox { Name = "txtCp", Location = new Point(180, 177), Size = new Size(80, 23) };
            var txtCommune = new TextBox { Name = "txtCommune", Location = new Point(270, 177), Size = new Size(210, 23) };

            // Pays
            var lblPays = new Label { Text = "Pays", Location = new Point(20, 210), Size = new Size(150, 23) };
            var cmbPays = new ComboBox { Name = "cmbPays", Location = new Point(180, 207), Size = new Size(200, 23) };
            cmbPays.Items.Add("France");
            cmbPays.DropDownStyle = ComboBoxStyle.DropDownList;

            // Devise
            var lblDevise = new Label { Text = "Devise", Location = new Point(20, 240), Size = new Size(150, 23) };
            var txtDevise = new TextBox { Name = "txtDevise", Location = new Point(180, 237), Size = new Size(100, 23) };

            // Téléphone
            var lblTel = new Label { Text = "Téléphone", Location = new Point(20, 270), Size = new Size(150, 23) };
            var txtTel = new TextBox { Name = "txtTel", Location = new Point(180, 267), Size = new Size(150, 23) };

            // Fax
            var lblFax = new Label { Text = "Fax", Location = new Point(350, 270), Size = new Size(50, 23) };
            var txtFax = new TextBox { Name = "txtFax", Location = new Point(410, 267), Size = new Size(150, 23) };

            // Email
            var lblEmail = new Label { Text = "Email", Location = new Point(20, 300), Size = new Size(150, 23) };
            var txtEmail = new TextBox { Name = "txtEmail", Location = new Point(180, 297), Size = new Size(300, 23) };

            // Site internet
            var lblSite = new Label { Text = "Site internet", Location = new Point(20, 330), Size = new Size(150, 23) };
            var txtSite = new TextBox { Name = "txtSite", Location = new Point(180, 327), Size = new Size(300, 23) };

            // Bouton Valider
            var btnValider = new Button { Text = "Valider", Location = new Point(250, 380), Size = new Size(100, 30) };
            btnValider.BackColor = Color.FromArgb(70, 70, 100);
            btnValider.ForeColor = Color.White;
            btnValider.Click += BtnValiderCoordonnees_Click;

            panel.Controls.AddRange(new Control[] {
                lblNom, txtNom, lblSuite, txtSuite, lblComplement, txtComplement,
                lblVoie, txtVoie, lblBp, txtBp, lblCp, txtCp, txtCommune,
                lblPays, cmbPays, lblDevise, txtDevise, lblTel, txtTel,
                lblFax, txtFax, lblEmail, txtEmail, lblSite, txtSite, btnValider
            });

            tabCoordonnees.Controls.Add(panel);
        }

        private void CreateButTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var lblBut = new Label { 
                Text = "But de l'association", 
                Location = new Point(20, 20), 
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            var txtBut = new TextBox { 
                Name = "txtBut", 
                Location = new Point(20, 50), 
                Size = new Size(600, 200),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            var lblInfo = new Label {
                Text = "Cette zone est destinée à saisir le but de l'association tel qu'il est décrit dans les statuts de votre association, comme par exemple :",
                Location = new Point(20, 270),
                Size = new Size(600, 40),
                Font = new Font("Segoe UI", 8F)
            };

            var lblExemples = new Label {
                Text = "- Proposer diverses activités manuelles, sportives et culturelles.\n- Animation du village par le village.\n- Apprentissage des langues vivantes.\n- etc...",
                Location = new Point(20, 320),
                Size = new Size(600, 80),
                Font = new Font("Segoe UI", 8F)
            };

            var btnValider = new Button { Text = "Valider", Location = new Point(300, 420), Size = new Size(100, 30) };
            btnValider.BackColor = Color.FromArgb(70, 70, 100);
            btnValider.ForeColor = Color.White;
            btnValider.Click += BtnValiderBut_Click;

            panel.Controls.AddRange(new Control[] { lblBut, txtBut, lblInfo, lblExemples, btnValider });
            tabBut.Controls.Add(panel);
        }

        private void CreateAdministrationTab()
        {
            // Implémentation similaire pour l'onglet Administration
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            
            var lblTitle = new Label { 
                Text = "Immatriculation de l'association", 
                Location = new Point(20, 20), 
                Size = new Size(300, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // Ajouter les contrôles pour l'administration
            panel.Controls.Add(lblTitle);
            tabAdministration.Controls.Add(panel);
        }

        private void CreatePresidentTab()
        {
            // Implémentation pour l'onglet Président
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            tabPresident.Controls.Add(panel);
        }

        private void CreateTresorierTab()
        {
            // Implémentation pour l'onglet Trésorier
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            tabTresorier.Controls.Add(panel);
        }

        private void CreateSecretaireTab()
        {
            // Implémentation pour l'onglet Secrétaire
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            tabSecretaire.Controls.Add(panel);
        }

        private void CreateLogoTab()
        {
            // Implémentation pour l'onglet Logo
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            tabLogo.Controls.Add(panel);
        }

        private async void LoadDataAsync()
        {
            try
            {
                _association = await _associationService.GetAssociationPrincipaleAsync();
                _president = await _associationService.GetPresidentAsync();
                _tresorier = await _associationService.GetTresorierAsync();
                _secretaire = await _associationService.GetSecretaireAsync();

                PopulateFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}", "Erreur", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PopulateFields()
        {
            if (_association == null) return;

            // Remplir les champs de l'onglet Coordonnées
            SetTextBoxValue("txtNom", _association.Nom);
            SetTextBoxValue("txtSuite", _association.SuiteDuNom);
            SetTextBoxValue("txtComplement", _association.ComplementAdresse);
            SetTextBoxValue("txtVoie", _association.NumeroEtNomVoie);
            SetTextBoxValue("txtBp", _association.BpOuLieuDit);
            SetTextBoxValue("txtCp", _association.CodePostal);
            SetTextBoxValue("txtCommune", _association.Commune);
            SetComboBoxValue("cmbPays", _association.Pays);
            SetTextBoxValue("txtDevise", _association.Devise);
            SetTextBoxValue("txtTel", _association.Telephone);
            SetTextBoxValue("txtFax", _association.Fax);
            SetTextBoxValue("txtEmail", _association.Email);
            SetTextBoxValue("txtSite", _association.SiteInternet);
            SetTextBoxValue("txtBut", _association.ButAssociation);
        }

        private void SetTextBoxValue(string name, string value)
        {
            var control = FindControlByName(name);
            if (control is TextBox textBox)
            {
                textBox.Text = value ?? string.Empty;
            }
        }

        private void SetComboBoxValue(string name, string value)
        {
            var control = FindControlByName(name);
            if (control is ComboBox comboBox)
            {
                comboBox.Text = value ?? string.Empty;
            }
        }

        private Control FindControlByName(string name)
        {
            return FindControlRecursive(this, name);
        }

        private Control FindControlRecursive(Control parent, string name)
        {
            foreach (Control control in parent.Controls)
            {
                if (control.Name == name)
                    return control;

                var found = FindControlRecursive(control, name);
                if (found != null)
                    return found;
            }
            return null;
        }

        private async void BtnValiderCoordonnees_Click(object sender, EventArgs e)
        {
            try
            {
                if (_association == null) return;

                // Récupérer les valeurs des champs
                _association.Nom = GetTextBoxValue("txtNom");
                _association.SuiteDuNom = GetTextBoxValue("txtSuite");
                _association.ComplementAdresse = GetTextBoxValue("txtComplement");
                _association.NumeroEtNomVoie = GetTextBoxValue("txtVoie");
                _association.BpOuLieuDit = GetTextBoxValue("txtBp");
                _association.CodePostal = GetTextBoxValue("txtCp");
                _association.Commune = GetTextBoxValue("txtCommune");
                _association.Pays = GetComboBoxValue("cmbPays");
                _association.Devise = GetTextBoxValue("txtDevise");
                _association.Telephone = GetTextBoxValue("txtTel");
                _association.Fax = GetTextBoxValue("txtFax");
                _association.Email = GetTextBoxValue("txtEmail");
                _association.SiteInternet = GetTextBoxValue("txtSite");

                var success = await _associationService.UpdateAssociationAsync(_association);
                
                if (success)
                {
                    MessageBox.Show("Coordonnées mises à jour avec succès!", "Succès", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Erreur lors de la mise à jour des coordonnées.", "Erreur", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur: {ex.Message}", "Erreur", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnValiderBut_Click(object sender, EventArgs e)
        {
            try
            {
                if (_association == null) return;

                _association.ButAssociation = GetTextBoxValue("txtBut");
                
                var success = await _associationService.UpdateAssociationAsync(_association);
                
                if (success)
                {
                    MessageBox.Show("But de l'association mis à jour avec succès!", "Succès", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Erreur lors de la mise à jour du but de l'association.", "Erreur", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur: {ex.Message}", "Erreur", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GetTextBoxValue(string name)
        {
            var control = FindControlByName(name);
            return control is TextBox textBox ? textBox.Text : string.Empty;
        }

        private string GetComboBoxValue(string name)
        {
            var control = FindControlByName(name);
            return control is ComboBox comboBox ? comboBox.Text : string.Empty;
        }
    }
}
