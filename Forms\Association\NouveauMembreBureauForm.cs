using System;
using System.Drawing;
using System.Windows.Forms;
using GestionAssociations.Models;

namespace GestionAssociations.Forms.Association
{
    public partial class NouveauMembreBureauForm : Form
    {
        private ComboBox cmbDateAssemblee;
        private ComboBox cmbAdherent;
        private ComboBox cmbSelectionAdherent;
        private TextBox txtLieuNaissance;
        private TextBox txtNationalite;
        private TextBox txtProfession;
        private ComboBox cmbFonction;
        private DateTimePicker dtpDateDebut;
        private DateTimePicker dtpDateFin;
        private RadioButton rbMembreBureau;
        private RadioButton rbMembreCA;
        private Button btnValider;
        private Button btnAnnuler;
        private Button btnAjouterFonction;

        private bool _isConseilAdministration;

        public MembreBureau Membre { get; private set; }

        public NouveauMembreBureauForm(bool isConseilAdministration = false)
        {
            _isConseilAdministration = isConseilAdministration;
            InitializeComponent();
            InitializeCustomComponents();
            LoadData();
        }

        private void InitializeCustomComponents()
        {
            this.Text = _isConseilAdministration ? "Nouveau membre du CA" : "Nouveau membre du bureau";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Panel principal avec titre
            var titlePanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(500, 40),
                BackColor = Color.FromArgb(70, 70, 100)
            };

            var titleLabel = new Label
            {
                Text = _isConseilAdministration ? "Nouveau membre du CA" : "Nouveau membre du bureau",
                Location = new Point(15, 10),
                Size = new Size(300, 20),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            titlePanel.Controls.Add(titleLabel);

            // Panel de contenu
            var contentPanel = new Panel
            {
                Location = new Point(0, 40),
                Size = new Size(500, 520),
                BackColor = Color.White,
                Padding = new Padding(20),
                AutoScroll = true
            };

            // Section Le membre
            var lblSectionMembre = new Label
            {
                Text = "Le membre",
                Location = new Point(20, 20),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // Date assemblée
            var lblDateAssemblee = new Label
            {
                Text = "Date assemblée",
                Location = new Point(20, 60),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            cmbDateAssemblee = new ComboBox
            {
                Location = new Point(150, 57),
                Size = new Size(200, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Adhérent
            var lblAdherent = new Label
            {
                Text = "Adhérent",
                Location = new Point(20, 100),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            cmbAdherent = new ComboBox
            {
                Location = new Point(150, 97),
                Size = new Size(300, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Sélection adhérent
            var lblSelectionAdherent = new Label
            {
                Text = "Sélection adhérent",
                Location = new Point(20, 140),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            cmbSelectionAdherent = new ComboBox
            {
                Location = new Point(150, 137),
                Size = new Size(300, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Lieu naissance
            var lblLieuNaissance = new Label
            {
                Text = "Lieu naissance",
                Location = new Point(20, 180),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            txtLieuNaissance = new TextBox
            {
                Location = new Point(150, 177),
                Size = new Size(300, 23)
            };

            // Nationalité
            var lblNationalite = new Label
            {
                Text = "Nationalité",
                Location = new Point(20, 220),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            txtNationalite = new TextBox
            {
                Location = new Point(150, 217),
                Size = new Size(300, 23)
            };

            // Profession
            var lblProfession = new Label
            {
                Text = "Profession",
                Location = new Point(20, 260),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            txtProfession = new TextBox
            {
                Location = new Point(150, 257),
                Size = new Size(300, 23)
            };

            // Section Son mandat
            var lblSectionMandat = new Label
            {
                Text = "Son mandat",
                Location = new Point(20, 300),
                Size = new Size(200, 23),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // Fonction
            var lblFonction = new Label
            {
                Text = "Fonction",
                Location = new Point(20, 340),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            cmbFonction = new ComboBox
            {
                Location = new Point(150, 337),
                Size = new Size(250, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            btnAjouterFonction = new Button
            {
                Text = "+",
                Location = new Point(410, 337),
                Size = new Size(30, 23),
                BackColor = Color.FromArgb(255, 165, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            // Date début
            var lblDateDebut = new Label
            {
                Text = "Date début",
                Location = new Point(20, 380),
                Size = new Size(80, 23),
                Font = new Font("Segoe UI", 9F)
            };

            dtpDateDebut = new DateTimePicker
            {
                Location = new Point(110, 377),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };

            // Au
            var lblAu = new Label
            {
                Text = "Au",
                Location = new Point(250, 380),
                Size = new Size(30, 23),
                Font = new Font("Segoe UI", 9F)
            };

            // Date fin
            dtpDateFin = new DateTimePicker
            {
                Location = new Point(290, 377),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today.AddYears(1)
            };

            // Radio buttons pour type de membre
            rbMembreBureau = new RadioButton
            {
                Text = "Membre du bureau",
                Location = new Point(150, 420),
                Size = new Size(150, 23),
                Checked = !_isConseilAdministration
            };

            rbMembreCA = new RadioButton
            {
                Text = "Membre du CA",
                Location = new Point(150, 450),
                Size = new Size(150, 23),
                Checked = _isConseilAdministration
            };

            // Boutons
            btnValider = new Button
            {
                Text = "Valider",
                Location = new Point(250, 490),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(70, 70, 100),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            btnAnnuler = new Button
            {
                Text = "Annuler",
                Location = new Point(360, 490),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(150, 150, 150),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            // Événements
            btnValider.Click += BtnValider_Click;
            btnAnnuler.Click += BtnAnnuler_Click;
            btnAjouterFonction.Click += BtnAjouterFonction_Click;

            // Ajouter les contrôles au panel de contenu
            contentPanel.Controls.AddRange(new Control[]
            {
                lblSectionMembre,
                lblDateAssemblee, cmbDateAssemblee,
                lblAdherent, cmbAdherent,
                lblSelectionAdherent, cmbSelectionAdherent,
                lblLieuNaissance, txtLieuNaissance,
                lblNationalite, txtNationalite,
                lblProfession, txtProfession,
                lblSectionMandat,
                lblFonction, cmbFonction, btnAjouterFonction,
                lblDateDebut, dtpDateDebut, lblAu, dtpDateFin,
                rbMembreBureau, rbMembreCA,
                btnValider, btnAnnuler
            });

            // Ajouter les panels au formulaire
            this.Controls.AddRange(new Control[] { titlePanel, contentPanel });
        }

        private void LoadData()
        {
            // Charger les dates d'assemblée
            cmbDateAssemblee.Items.Add("08/01/2025");
            cmbDateAssemblee.SelectedIndex = 0;

            // Charger les adhérents (simulation)
            cmbAdherent.Items.AddRange(new string[]
            {
                "DUPONT Jean",
                "MARTIN Marie",
                "BERNARD Pierre",
                "THOMAS Catherine"
            });

            // Charger les fonctions
            cmbFonction.Items.AddRange(new string[]
            {
                "Président",
                "Vice-Président",
                "Trésorier",
                "Secrétaire",
                "Membre",
                "Assesseur"
            });
        }

        private void BtnValider_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                Membre = new MembreBureau
                {
                    NomPrenom = cmbAdherent.Text,
                    LieuNaissance = txtLieuNaissance.Text.Trim(),
                    Nationalite = txtNationalite.Text.Trim(),
                    Profession = txtProfession.Text.Trim(),
                    Fonction = cmbFonction.Text,
                    DateDebut = dtpDateDebut.Value,
                    DateFin = dtpDateFin.Value,
                    EstMembreBureau = rbMembreBureau.Checked,
                    EstMembreCA = rbMembreCA.Checked,
                    EstActif = true,
                    DateCreation = DateTime.Now
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnAnnuler_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void BtnAjouterFonction_Click(object sender, EventArgs e)
        {
            // TODO: Ouvrir un formulaire pour ajouter une nouvelle fonction
            MessageBox.Show("Ajouter nouvelle fonction - À implémenter", "Information");
        }

        private bool ValidateInput()
        {
            if (cmbAdherent.SelectedIndex == -1)
            {
                MessageBox.Show("Veuillez sélectionner un adhérent.", "Validation", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbAdherent.Focus();
                return false;
            }

            if (cmbFonction.SelectedIndex == -1)
            {
                MessageBox.Show("Veuillez sélectionner une fonction.", "Validation", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbFonction.Focus();
                return false;
            }

            if (dtpDateFin.Value <= dtpDateDebut.Value)
            {
                MessageBox.Show("La date de fin doit être postérieure à la date de début.", "Validation", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dtpDateFin.Focus();
                return false;
            }

            return true;
        }
    }
}
