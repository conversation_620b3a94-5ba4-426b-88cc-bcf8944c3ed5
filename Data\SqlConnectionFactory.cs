using System.Configuration;
using System.Data;
using System.Data.SqlClient;

namespace GestionAssociations.Data
{
    /// <summary>
    /// Factory pour les connexions SQL Server
    /// </summary>
    public class SqlConnectionFactory : IConnectionFactory
    {
        private readonly string _connectionString;

        public SqlConnectionFactory()
        {
            _connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                ?? throw new ConfigurationErrorsException("La chaîne de connexion 'DefaultConnection' n'est pas configurée.");
        }

        public SqlConnectionFactory(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        public string ConnectionString => _connectionString;

        public IDbConnection CreateConnection()
        {
            var connection = new SqlConnection(_connectionString);
            return connection;
        }
    }
}
