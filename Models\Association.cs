using System;

namespace GestionAssociations.Models
{
    public class Association
    {
        public int Id { get; set; }
        public string Nom { get; set; }
        public string SuiteDuNom { get; set; }
        public string ComplementAdresse { get; set; }
        public string NumeroEtNomVoie { get; set; }
        public string BpOuLieuDit { get; set; }
        public string CodePostal { get; set; }
        public string Commune { get; set; }
        public string Pays { get; set; }
        public string Devise { get; set; }
        public string Telephone { get; set; }
        public string Fax { get; set; }
        public string Email { get; set; }
        public string SiteInternet { get; set; }
        public string ButAssociation { get; set; }
        public string ReferencePrefecture { get; set; }
        public string AgrementPrefecture { get; set; }
        public string NumeroIntraCommunitaire { get; set; }
        public string RegistreCommerce { get; set; }
        public string CodeNaf { get; set; }
        public string Siret { get; set; }
        public string Banque { get; set; }
        public string Agence { get; set; }
        public string Etablissement { get; set; }
        public string CodeGuichet { get; set; }
        public string NumeroCompte { get; set; }
        public string CleRib { get; set; }
        public string Iban { get; set; }
        public string Bic { get; set; }
        public string IdentifiantSepa { get; set; }
        public byte[] Logo { get; set; }
        public int LogoHauteur { get; set; }
        public int LogoLargeur { get; set; }
        public DateTime DateCreation { get; set; }
        public DateTime DateModification { get; set; }
        public bool GestionParPoles { get; set; }
    }

    public class President
    {
        public int Id { get; set; }
        public int AssociationId { get; set; }
        public string NomPresident { get; set; }
        public string ComplementAdresse { get; set; }
        public string Adresse { get; set; }
        public string BpOuLieuDit { get; set; }
        public string CodePostal { get; set; }
        public string Commune { get; set; }
        public string Telephone { get; set; }
        public string Portable { get; set; }
        public string Email { get; set; }
    }

    public class Tresorier
    {
        public int Id { get; set; }
        public int AssociationId { get; set; }
        public string NomTresorier { get; set; }
        public string ComplementAdresse { get; set; }
        public string Adresse { get; set; }
        public string BpOuLieuDit { get; set; }
        public string CodePostal { get; set; }
        public string Commune { get; set; }
        public string Telephone { get; set; }
        public string Portable { get; set; }
        public string Email { get; set; }
    }

    public class Secretaire
    {
        public int Id { get; set; }
        public int AssociationId { get; set; }
        public string NomSecretaire { get; set; }
        public string ComplementAdresse { get; set; }
        public string Adresse { get; set; }
        public string BpOuLieuDit { get; set; }
        public string CodePostal { get; set; }
        public string Commune { get; set; }
        public string Telephone { get; set; }
        public string Portable { get; set; }
        public string Email { get; set; }
    }
}
