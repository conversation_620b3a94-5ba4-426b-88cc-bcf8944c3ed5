-- Script de création de la base de données Gestion des Associations
-- Compatible avec SQL Server

USE master;
GO

-- Créer la base de données si elle n'existe pas
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'GestionAssociations')
BEGIN
    CREATE DATABASE GestionAssociations;
END
GO

USE GestionAssociations;
GO

-- Table Associations
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Associations' AND xtype='U')
BEGIN
    CREATE TABLE Associations (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Nom nvarchar(255) NOT NULL,
        SuiteDuNom nvarchar(255),
        ComplementAdresse nvarchar(255),
        NumeroEtNomVoie nvarchar(255),
        BpOuLieuDit nvarchar(255),
        CodePostal nvarchar(10),
        Commune nvarchar(255),
        Pays nvarchar(100) DEFAULT 'France',
        <PERSON><PERSON> nvarchar(10) DEFAULT 'Euro',
        Telephone nvarchar(20),
        Fax nvarchar(20),
        <PERSON>ail nvarchar(255),
        SiteInternet nvarchar(255),
        ButAssociation ntext,
        ReferencePrefecture nvarchar(100),
        AgrementPrefecture nvarchar(100),
        NumeroIntraCommunitaire nvarchar(50),
        RegistreCommerce nvarchar(100),
        CodeNaf nvarchar(10),
        Siret nvarchar(20),
        Banque nvarchar(255),
        Agence nvarchar(255),
        Etablissement nvarchar(50),
        CodeGuichet nvarchar(10),
        NumeroCompte nvarchar(20),
        CleRib nvarchar(5),
        Iban nvarchar(50),
        Bic nvarchar(20),
        IdentifiantSepa nvarchar(50),
        Logo varbinary(max),
        LogoHauteur int DEFAULT 120,
        LogoLargeur int DEFAULT 120,
        GestionParPoles bit DEFAULT 0,
        DateCreation datetime2 DEFAULT GETDATE(),
        DateModification datetime2 DEFAULT GETDATE()
    );
END
GO

-- Table Presidents
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Presidents' AND xtype='U')
BEGIN
    CREATE TABLE Presidents (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AssociationId int NOT NULL,
        NomPresident nvarchar(255),
        ComplementAdresse nvarchar(255),
        Adresse nvarchar(255),
        BpOuLieuDit nvarchar(255),
        CodePostal nvarchar(10),
        Commune nvarchar(255),
        Telephone nvarchar(20),
        Portable nvarchar(20),
        Email nvarchar(255),
        FOREIGN KEY (AssociationId) REFERENCES Associations(Id)
    );
END
GO

-- Table Tresoriers
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Tresoriers' AND xtype='U')
BEGIN
    CREATE TABLE Tresoriers (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AssociationId int NOT NULL,
        NomTresorier nvarchar(255),
        ComplementAdresse nvarchar(255),
        Adresse nvarchar(255),
        BpOuLieuDit nvarchar(255),
        CodePostal nvarchar(10),
        Commune nvarchar(255),
        Telephone nvarchar(20),
        Portable nvarchar(20),
        Email nvarchar(255),
        FOREIGN KEY (AssociationId) REFERENCES Associations(Id)
    );
END
GO

-- Table Secretaires
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Secretaires' AND xtype='U')
BEGIN
    CREATE TABLE Secretaires (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AssociationId int NOT NULL,
        NomSecretaire nvarchar(255),
        ComplementAdresse nvarchar(255),
        Adresse nvarchar(255),
        BpOuLieuDit nvarchar(255),
        CodePostal nvarchar(10),
        Commune nvarchar(255),
        Telephone nvarchar(20),
        Portable nvarchar(20),
        Email nvarchar(255),
        FOREIGN KEY (AssociationId) REFERENCES Associations(Id)
    );
END
GO

-- Table Poles
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Poles' AND xtype='U')
BEGIN
    CREATE TABLE Poles (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Libelle nvarchar(255) NOT NULL,
        Description ntext,
        EstActif bit DEFAULT 1,
        DateCreation datetime2 DEFAULT GETDATE(),
        DateModification datetime2 DEFAULT GETDATE()
    );
END
GO

-- Table Secteurs
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Secteurs' AND xtype='U')
BEGIN
    CREATE TABLE Secteurs (
        Id int IDENTITY(1,1) PRIMARY KEY,
        PoleId int NOT NULL,
        Libelle nvarchar(255) NOT NULL,
        Description ntext,
        EstActif bit DEFAULT 1,
        DateCreation datetime2 DEFAULT GETDATE(),
        FOREIGN KEY (PoleId) REFERENCES Poles(Id)
    );
END
GO

-- Insérer des données de base
IF NOT EXISTS (SELECT * FROM Associations)
BEGIN
    INSERT INTO Associations (Nom, Pays, Devise, GestionParPoles)
    VALUES ('B-ASSOCIATION DEMO', 'France', 'Euro', 0);
    
    DECLARE @AssociationId int = SCOPE_IDENTITY();
    
    INSERT INTO Presidents (AssociationId) VALUES (@AssociationId);
    INSERT INTO Tresoriers (AssociationId) VALUES (@AssociationId);
    INSERT INTO Secretaires (AssociationId) VALUES (@AssociationId);
END
GO

-- Table AssembleesGenerales
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AssembleesGenerales' AND xtype='U')
BEGIN
    CREATE TABLE AssembleesGenerales (
        Id int IDENTITY(1,1) PRIMARY KEY,
        DateAssemblee datetime2 NOT NULL,
        Description nvarchar(255),
        Observations ntext,
        DateCreation datetime2 DEFAULT GETDATE(),
        DateModification datetime2 DEFAULT GETDATE()
    );
END
GO

-- Table CommissairesAuxComptes
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CommissairesAuxComptes' AND xtype='U')
BEGIN
    CREATE TABLE CommissairesAuxComptes (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AssembleeId int NOT NULL,
        NomPrenom nvarchar(255),
        Fonction nvarchar(100),
        Cabinet nvarchar(255),
        DateDebut datetime2,
        DateFin datetime2,
        EstActif bit DEFAULT 1,
        DateCreation datetime2 DEFAULT GETDATE(),
        FOREIGN KEY (AssembleeId) REFERENCES AssembleesGenerales(Id)
    );
END
GO

-- Table MembresBureau
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MembresBureau' AND xtype='U')
BEGIN
    CREATE TABLE MembresBureau (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AssembleeId int NOT NULL,
        AdherentId int,
        NomPrenom nvarchar(255),
        Fonction nvarchar(100),
        Email nvarchar(255),
        Telephone nvarchar(20),
        LieuNaissance nvarchar(255),
        Nationalite nvarchar(100),
        Profession nvarchar(255),
        DateDebut datetime2,
        DateFin datetime2,
        EstMembreBureau bit DEFAULT 0,
        EstMembreCA bit DEFAULT 0,
        EstActif bit DEFAULT 1,
        DateCreation datetime2 DEFAULT GETDATE(),
        FOREIGN KEY (AssembleeId) REFERENCES AssembleesGenerales(Id)
    );
END
GO

-- Table FonctionsBureau
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FonctionsBureau' AND xtype='U')
BEGIN
    CREATE TABLE FonctionsBureau (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Libelle nvarchar(255) NOT NULL,
        Description ntext,
        Ordre int DEFAULT 0,
        EstActif bit DEFAULT 1,
        DateCreation datetime2 DEFAULT GETDATE()
    );
END
GO

-- Insérer des pôles par défaut
IF NOT EXISTS (SELECT * FROM Poles)
BEGIN
    INSERT INTO Poles (Libelle, Description) VALUES
    ('Formation Musicale', 'Pôle dédié à la formation musicale'),
    ('Général', 'Pôle général de l''association'),
    ('Instrument', 'Pôle pour l''apprentissage des instruments'),
    ('Manifestations', 'Pôle pour l''organisation des manifestations'),
    ('Pratique Collective', 'Pôle pour les activités collectives');
END
GO

-- Insérer des fonctions par défaut
IF NOT EXISTS (SELECT * FROM FonctionsBureau)
BEGIN
    INSERT INTO FonctionsBureau (Libelle, Description, Ordre) VALUES
    ('Président', 'Président de l''association', 1),
    ('Vice-Président', 'Vice-Président de l''association', 2),
    ('Trésorier', 'Trésorier de l''association', 3),
    ('Secrétaire', 'Secrétaire de l''association', 4),
    ('Membre', 'Membre du bureau', 5),
    ('Assesseur', 'Assesseur', 6),
    ('Commissaire aux comptes', 'Commissaire aux comptes', 7);
END
GO

-- Table Medecins
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Medecins' AND xtype='U')
BEGIN
    CREATE TABLE Medecins (
        Id int IDENTITY(1,1) PRIMARY KEY,
        NomPrenom nvarchar(255) NOT NULL,
        ComplementAdresse nvarchar(255),
        NumeroVoie nvarchar(50),
        NomVoie nvarchar(255),
        CodePostal nvarchar(10),
        Ville nvarchar(100),
        Telephone nvarchar(20),
        Mobile nvarchar(20),
        NumeroOrdre nvarchar(50),
        Fax nvarchar(20),
        EstActif bit DEFAULT 1,
        DateCreation datetime2 DEFAULT GETDATE()
    );
END
GO

-- Table PersonnesAPrevenir
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PersonnesAPrevenir' AND xtype='U')
BEGIN
    CREATE TABLE PersonnesAPrevenir (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AdherentId int NOT NULL,
        NomPrenom nvarchar(255),
        TelDomicile nvarchar(20),
        TelBureau nvarchar(20),
        TelPortable nvarchar(20),
        Email nvarchar(255),
        DateCreation datetime2 DEFAULT GETDATE(),
        FOREIGN KEY (AdherentId) REFERENCES Adherents(Id)
    );
END
GO

-- Table AdherentsSections
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AdherentsSections' AND xtype='U')
BEGIN
    CREATE TABLE AdherentsSections (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AdherentId int NOT NULL,
        SectionId int NOT NULL,
        LibelleSection nvarchar(255),
        Periode nvarchar(50),
        DateCreation datetime2 DEFAULT GETDATE(),
        FOREIGN KEY (AdherentId) REFERENCES Adherents(Id),
        FOREIGN KEY (SectionId) REFERENCES Sections(Id)
    );
END
GO

-- Table CotisationsAdherents
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CotisationsAdherents' AND xtype='U')
BEGIN
    CREATE TABLE CotisationsAdherents (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AdherentId int NOT NULL,
        Periode nvarchar(50),
        Libelle nvarchar(255),
        Montant decimal(10,2),
        DatePaiement datetime2,
        ModePaiement nvarchar(100),
        DateCreation datetime2 DEFAULT GETDATE(),
        FOREIGN KEY (AdherentId) REFERENCES Adherents(Id)
    );
END
GO

-- Table FichiersAdherents
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FichiersAdherents' AND xtype='U')
BEGIN
    CREATE TABLE FichiersAdherents (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AdherentId int NOT NULL,
        NomDocument nvarchar(255),
        CheminFichier nvarchar(500),
        DateAjout datetime2 DEFAULT GETDATE(),
        TailleFichier bigint,
        TypeMime nvarchar(100),
        FOREIGN KEY (AdherentId) REFERENCES Adherents(Id)
    );
END
GO

-- Table EmailsAdherents
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmailsAdherents' AND xtype='U')
BEGIN
    CREATE TABLE EmailsAdherents (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AdherentId int NOT NULL,
        DateEnvoi datetime2 DEFAULT GETDATE(),
        Sujet nvarchar(255),
        Corps ntext,
        PieceJointe bit DEFAULT 0,
        StatutEnvoi nvarchar(50),
        FOREIGN KEY (AdherentId) REFERENCES Adherents(Id)
    );
END
GO

-- Insérer une assemblée générale par défaut
IF NOT EXISTS (SELECT * FROM AssembleesGenerales)
BEGIN
    INSERT INTO AssembleesGenerales (DateAssemblee, Description, Observations)
    VALUES ('2025-01-08', 'Assemblée générale', 'Assemblée générale annuelle');
END
GO

-- Table Logs pour NLog
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Logs' AND xtype='U')
BEGIN
    CREATE TABLE Logs (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Date datetime2 NOT NULL,
        Level nvarchar(50) NOT NULL,
        Logger nvarchar(255) NOT NULL,
        Message nvarchar(max),
        Exception nvarchar(max),
        MachineName nvarchar(100),
        UserName nvarchar(100),
        DateCreated datetime2 DEFAULT GETDATE()
    );

    -- Index pour améliorer les performances des requêtes de logs
    CREATE INDEX IX_Logs_Date ON Logs(Date);
    CREATE INDEX IX_Logs_Level ON Logs(Level);
    CREATE INDEX IX_Logs_Logger ON Logs(Logger);
END
GO

-- Insérer des médecins par défaut
IF NOT EXISTS (SELECT * FROM Medecins)
BEGIN
    INSERT INTO Medecins (NomPrenom, Ville, Telephone) VALUES
    ('Dr. MARTIN Pierre', 'Paris', '***********.89'),
    ('Dr. DURAND Marie', 'Lyon', '04.56.78.90.12'),
    ('Dr. BERNARD Jean', 'Marseille', '04.91.23.45.67');
END
GO

PRINT 'Base de données GestionAssociations créée avec succès!';
