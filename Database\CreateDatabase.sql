-- Script de création de la base de données Gestion des Associations
-- Compatible avec SQL Server

USE master;
GO

-- Créer la base de données si elle n'existe pas
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'GestionAssociations')
BEGIN
    CREATE DATABASE GestionAssociations;
END
GO

USE GestionAssociations;
GO

-- Table Associations
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Associations' AND xtype='U')
BEGIN
    CREATE TABLE Associations (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Nom nvarchar(255) NOT NULL,
        SuiteDuNom nvarchar(255),
        ComplementAdresse nvarchar(255),
        NumeroEtNomVoie nvarchar(255),
        BpOuLieuDit nvarchar(255),
        CodePostal nvarchar(10),
        Commune nvarchar(255),
        Pays nvarchar(100) DEFAULT 'France',
        <PERSON><PERSON> nvarchar(10) DEFAULT 'Euro',
        Telephone nvarchar(20),
        Fax nvarchar(20),
        <PERSON>ail nvarchar(255),
        SiteInternet nvarchar(255),
        ButAssociation ntext,
        ReferencePrefecture nvarchar(100),
        AgrementPrefecture nvarchar(100),
        NumeroIntraCommunitaire nvarchar(50),
        RegistreCommerce nvarchar(100),
        CodeNaf nvarchar(10),
        Siret nvarchar(20),
        Banque nvarchar(255),
        Agence nvarchar(255),
        Etablissement nvarchar(50),
        CodeGuichet nvarchar(10),
        NumeroCompte nvarchar(20),
        CleRib nvarchar(5),
        Iban nvarchar(50),
        Bic nvarchar(20),
        IdentifiantSepa nvarchar(50),
        Logo varbinary(max),
        LogoHauteur int DEFAULT 120,
        LogoLargeur int DEFAULT 120,
        GestionParPoles bit DEFAULT 0,
        DateCreation datetime2 DEFAULT GETDATE(),
        DateModification datetime2 DEFAULT GETDATE()
    );
END
GO

-- Table Presidents
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Presidents' AND xtype='U')
BEGIN
    CREATE TABLE Presidents (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AssociationId int NOT NULL,
        NomPresident nvarchar(255),
        ComplementAdresse nvarchar(255),
        Adresse nvarchar(255),
        BpOuLieuDit nvarchar(255),
        CodePostal nvarchar(10),
        Commune nvarchar(255),
        Telephone nvarchar(20),
        Portable nvarchar(20),
        Email nvarchar(255),
        FOREIGN KEY (AssociationId) REFERENCES Associations(Id)
    );
END
GO

-- Table Tresoriers
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Tresoriers' AND xtype='U')
BEGIN
    CREATE TABLE Tresoriers (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AssociationId int NOT NULL,
        NomTresorier nvarchar(255),
        ComplementAdresse nvarchar(255),
        Adresse nvarchar(255),
        BpOuLieuDit nvarchar(255),
        CodePostal nvarchar(10),
        Commune nvarchar(255),
        Telephone nvarchar(20),
        Portable nvarchar(20),
        Email nvarchar(255),
        FOREIGN KEY (AssociationId) REFERENCES Associations(Id)
    );
END
GO

-- Table Secretaires
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Secretaires' AND xtype='U')
BEGIN
    CREATE TABLE Secretaires (
        Id int IDENTITY(1,1) PRIMARY KEY,
        AssociationId int NOT NULL,
        NomSecretaire nvarchar(255),
        ComplementAdresse nvarchar(255),
        Adresse nvarchar(255),
        BpOuLieuDit nvarchar(255),
        CodePostal nvarchar(10),
        Commune nvarchar(255),
        Telephone nvarchar(20),
        Portable nvarchar(20),
        Email nvarchar(255),
        FOREIGN KEY (AssociationId) REFERENCES Associations(Id)
    );
END
GO

-- Table Poles
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Poles' AND xtype='U')
BEGIN
    CREATE TABLE Poles (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Libelle nvarchar(255) NOT NULL,
        Description ntext,
        EstActif bit DEFAULT 1,
        DateCreation datetime2 DEFAULT GETDATE(),
        DateModification datetime2 DEFAULT GETDATE()
    );
END
GO

-- Table Secteurs
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Secteurs' AND xtype='U')
BEGIN
    CREATE TABLE Secteurs (
        Id int IDENTITY(1,1) PRIMARY KEY,
        PoleId int NOT NULL,
        Libelle nvarchar(255) NOT NULL,
        Description ntext,
        EstActif bit DEFAULT 1,
        DateCreation datetime2 DEFAULT GETDATE(),
        FOREIGN KEY (PoleId) REFERENCES Poles(Id)
    );
END
GO

-- Insérer des données de base
IF NOT EXISTS (SELECT * FROM Associations)
BEGIN
    INSERT INTO Associations (Nom, Pays, Devise, GestionParPoles)
    VALUES ('B-ASSOCIATION DEMO', 'France', 'Euro', 0);
    
    DECLARE @AssociationId int = SCOPE_IDENTITY();
    
    INSERT INTO Presidents (AssociationId) VALUES (@AssociationId);
    INSERT INTO Tresoriers (AssociationId) VALUES (@AssociationId);
    INSERT INTO Secretaires (AssociationId) VALUES (@AssociationId);
END
GO

-- Insérer des pôles par défaut
IF NOT EXISTS (SELECT * FROM Poles)
BEGIN
    INSERT INTO Poles (Libelle, Description) VALUES 
    ('Formation Musicale', 'Pôle dédié à la formation musicale'),
    ('Général', 'Pôle général de l''association'),
    ('Instrument', 'Pôle pour l''apprentissage des instruments'),
    ('Manifestations', 'Pôle pour l''organisation des manifestations'),
    ('Pratique Collective', 'Pôle pour les activités collectives');
END
GO

PRINT 'Base de données GestionAssociations créée avec succès!';
