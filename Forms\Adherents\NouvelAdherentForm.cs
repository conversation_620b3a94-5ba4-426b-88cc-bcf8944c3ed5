using System;
using System.Drawing;
using System.Windows.Forms;
using GestionAssociations.Models;

namespace GestionAssociations.Forms.Adherents
{
    public partial class NouvelAdherentForm : Form
    {
        private TabControl tabControl;
        private TabPage tabIdentite;
        private TabPage tabAdresses;
        private TabPage tabAdhesion;
        private TabPage tabChampsPersonnalises;
        private TabPage tabAffectationsCotisations;
        private TabPage tabSuiviMedical;
        private TabPage tabPersonnesAPrevenir;
        private TabPage tabFichiersEmails;

        // Contrôles Identité
        private TextBox txtRaisonSociale;
        private ComboBox cmbCivilite;
        private TextBox txtNom;
        private TextBox txtPrenom;
        private TextBox txtRepresentantLegal;
        private DateTimePicker dtpDateNaissance;
        private CheckBox chkNonArchive;
        private Label lblNumeroAdherent;
        private PictureBox picPhoto;

        // Boutons
        private Button btnValider;
        private Button btnAnnuler;

        public Adherent Adherent { get; private set; }

        public NouvelAdherentForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            LoadData();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "Nouvel adhérent";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterParent;

            // En-tête avec titre et switch Non archivé
            var headerPanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(1000, 50),
                BackColor = Color.FromArgb(70, 70, 100)
            };

            var titleLabel = new Label
            {
                Text = "Nouvel adhérent",
                Location = new Point(20, 15),
                Size = new Size(200, 20),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12F, FontStyle.Bold)
            };

            chkNonArchive = new CheckBox
            {
                Text = "Non archivé",
                Location = new Point(800, 15),
                Size = new Size(100, 20),
                ForeColor = Color.White,
                Checked = true,
                Font = new Font("Segoe UI", 9F)
            };

            headerPanel.Controls.AddRange(new Control[] { titleLabel, chkNonArchive });

            // Panel principal avec identité et photo
            var mainPanel = new Panel
            {
                Location = new Point(0, 50),
                Size = new Size(1000, 120),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            CreateIdentitySection(mainPanel);

            // TabControl
            tabControl = new TabControl
            {
                Location = new Point(0, 170),
                Size = new Size(1000, 450),
                Font = new Font("Segoe UI", 9F)
            };

            CreateTabs();

            // Panel de boutons
            var buttonPanel = new Panel
            {
                Location = new Point(0, 620),
                Size = new Size(1000, 50),
                BackColor = Color.FromArgb(240, 240, 240)
            };

            btnValider = new Button
            {
                Text = "Valider",
                Location = new Point(800, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(70, 70, 100),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            btnAnnuler = new Button
            {
                Text = "Annuler",
                Location = new Point(890, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(150, 150, 150),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            // Événements
            btnValider.Click += BtnValider_Click;
            btnAnnuler.Click += BtnAnnuler_Click;

            buttonPanel.Controls.AddRange(new Control[] { btnValider, btnAnnuler });

            // Ajouter tous les contrôles au formulaire
            this.Controls.AddRange(new Control[] { headerPanel, mainPanel, tabControl, buttonPanel });
        }

        private void CreateIdentitySection(Panel parent)
        {
            // Section Identité de l'adhérent
            var lblIdentite = new Label
            {
                Text = "Identité de l'adhérent",
                Location = new Point(20, 10),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // Dernière mise à jour
            var lblDerniereMaj = new Label
            {
                Text = "Dernière mise à jour",
                Location = new Point(200, 10),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 9F)
            };

            var lblDateMaj = new Label
            {
                Text = "__/__/____ 00:00",
                Location = new Point(330, 10),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 9F),
                ForeColor = Color.Gray
            };

            // N° d'adhérent
            var lblNumero = new Label
            {
                Text = "N° d'adhérent",
                Location = new Point(550, 10),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            lblNumeroAdherent = new Label
            {
                Text = "109",
                Location = new Point(640, 10),
                Size = new Size(50, 20),
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(70, 70, 100)
            };

            // Photo
            picPhoto = new PictureBox
            {
                Location = new Point(750, 10),
                Size = new Size(100, 100),
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.FromArgb(240, 240, 240),
                SizeMode = PictureBoxSizeMode.StretchImage
            };

            // Raison sociale
            var lblRaisonSociale = new Label
            {
                Text = "Raison sociale",
                Location = new Point(20, 40),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 9F)
            };

            txtRaisonSociale = new TextBox
            {
                Location = new Point(130, 37),
                Size = new Size(300, 23)
            };

            // Civilité
            var lblCivilite = new Label
            {
                Text = "⚤ Civilité",
                Location = new Point(20, 70),
                Size = new Size(60, 20),
                Font = new Font("Segoe UI", 9F)
            };

            cmbCivilite = new ComboBox
            {
                Location = new Point(90, 67),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // Radio buttons pour civilité
            var rbMasculin = new RadioButton
            {
                Text = "Masculin",
                Location = new Point(200, 70),
                Size = new Size(80, 20),
                Checked = true
            };

            var rbFeminin = new RadioButton
            {
                Text = "Féminin",
                Location = new Point(290, 70),
                Size = new Size(80, 20)
            };

            var rbAutre = new RadioButton
            {
                Text = "Autre",
                Location = new Point(380, 70),
                Size = new Size(60, 20)
            };

            // Nom et prénom
            var lblNomPrenom = new Label
            {
                Text = "Nom et prénom",
                Location = new Point(20, 100),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 9F)
            };

            txtNom = new TextBox
            {
                Location = new Point(130, 97),
                Size = new Size(150, 23)
            };

            txtPrenom = new TextBox
            {
                Location = new Point(290, 97),
                Size = new Size(150, 23)
            };

            // Représentant légal
            var lblRepresentant = new Label
            {
                Text = "Représentant légal",
                Location = new Point(450, 40),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 9F)
            };

            txtRepresentantLegal = new TextBox
            {
                Location = new Point(450, 67),
                Size = new Size(200, 23)
            };

            // Date de naissance
            var lblDateNaissance = new Label
            {
                Text = "Date de naissance",
                Location = new Point(450, 100),
                Size = new Size(120, 20),
                Font = new Font("Segoe UI", 9F)
            };

            dtpDateNaissance = new DateTimePicker
            {
                Location = new Point(570, 97),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                ShowCheckBox = true,
                Checked = false
            };

            parent.Controls.AddRange(new Control[]
            {
                lblIdentite, lblDerniereMaj, lblDateMaj, lblNumero, lblNumeroAdherent, picPhoto,
                lblRaisonSociale, txtRaisonSociale,
                lblCivilite, cmbCivilite, rbMasculin, rbFeminin, rbAutre,
                lblNomPrenom, txtNom, txtPrenom,
                lblRepresentant, txtRepresentantLegal,
                lblDateNaissance, dtpDateNaissance
            });
        }

        private void CreateTabs()
        {
            // Onglet Adresses
            tabAdresses = new TabPage("Adresses");
            CreateAdressesTab();

            // Onglet Adhésion
            tabAdhesion = new TabPage("Adhésion");
            CreateAdhesionTab();

            // Onglet Champs personnalisés
            tabChampsPersonnalises = new TabPage("Champs personnalisés");
            CreateChampsPersonnalisesTab();

            // Onglet Affectations / Cotisations
            tabAffectationsCotisations = new TabPage("Affectations / Cotisations");
            CreateAffectationsCotisationsTab();

            // Onglet Suivi médical
            tabSuiviMedical = new TabPage("Suivi médical");
            CreateSuiviMedicalTab();

            // Onglet Personnes à prévenir
            tabPersonnesAPrevenir = new TabPage("Personnes à prévenir");
            CreatePersonnesAPrevenirTab();

            // Onglet Fichiers / Emails
            tabFichiersEmails = new TabPage("Fichiers / Emails");
            CreateFichiersEmailsTab();

            tabControl.TabPages.AddRange(new TabPage[]
            {
                tabAdresses, tabAdhesion, tabChampsPersonnalises,
                tabAffectationsCotisations, tabSuiviMedical,
                tabPersonnesAPrevenir, tabFichiersEmails
            });

            // Sélectionner le premier onglet par défaut
            tabControl.SelectedTab = tabAdresses;
        }

        private void CreateAdressesTab()
        {
            // TODO: Implémenter l'onglet Adresses
            var label = new Label
            {
                Text = "Onglet Adresses - À implémenter",
                Location = new Point(20, 20),
                Size = new Size(300, 20),
                Font = new Font("Segoe UI", 10F)
            };
            tabAdresses.Controls.Add(label);
        }

        private void CreateAdhesionTab()
        {
            // TODO: Implémenter l'onglet Adhésion
            var label = new Label
            {
                Text = "Onglet Adhésion - À implémenter",
                Location = new Point(20, 20),
                Size = new Size(300, 20),
                Font = new Font("Segoe UI", 10F)
            };
            tabAdhesion.Controls.Add(label);
        }

        private void CreateChampsPersonnalisesTab()
        {
            // TODO: Implémenter l'onglet Champs personnalisés
            var label = new Label
            {
                Text = "Onglet Champs personnalisés - À implémenter",
                Location = new Point(20, 20),
                Size = new Size(300, 20),
                Font = new Font("Segoe UI", 10F)
            };
            tabChampsPersonnalises.Controls.Add(label);
        }

        private void CreateAffectationsCotisationsTab()
        {
            // TODO: Implémenter l'onglet Affectations / Cotisations
            var label = new Label
            {
                Text = "Onglet Affectations / Cotisations - À implémenter",
                Location = new Point(20, 20),
                Size = new Size(300, 20),
                Font = new Font("Segoe UI", 10F)
            };
            tabAffectationsCotisations.Controls.Add(label);
        }

        private void CreateSuiviMedicalTab()
        {
            // TODO: Implémenter l'onglet Suivi médical
            var label = new Label
            {
                Text = "Onglet Suivi médical - À implémenter",
                Location = new Point(20, 20),
                Size = new Size(300, 20),
                Font = new Font("Segoe UI", 10F)
            };
            tabSuiviMedical.Controls.Add(label);
        }

        private void CreatePersonnesAPrevenirTab()
        {
            // TODO: Implémenter l'onglet Personnes à prévenir
            var label = new Label
            {
                Text = "Onglet Personnes à prévenir - À implémenter",
                Location = new Point(20, 20),
                Size = new Size(300, 20),
                Font = new Font("Segoe UI", 10F)
            };
            tabPersonnesAPrevenir.Controls.Add(label);
        }

        private void CreateFichiersEmailsTab()
        {
            // TODO: Implémenter l'onglet Fichiers / Emails
            var label = new Label
            {
                Text = "Onglet Fichiers / Emails - À implémenter",
                Location = new Point(20, 20),
                Size = new Size(300, 20),
                Font = new Font("Segoe UI", 10F)
            };
            tabFichiersEmails.Controls.Add(label);
        }

        private void LoadData()
        {
            // Charger les civilités
            cmbCivilite.Items.AddRange(new string[] { "M.", "Mme", "Mlle" });
            cmbCivilite.SelectedIndex = 0;

            // Générer un nouveau numéro d'adhérent
            lblNumeroAdherent.Text = "109"; // TODO: Générer automatiquement
        }

        private void BtnValider_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                // TODO: Créer l'objet Adherent avec toutes les données
                Adherent = new Adherent
                {
                    RaisonSociale = txtRaisonSociale.Text.Trim(),
                    Civilite = cmbCivilite.Text,
                    Nom = txtNom.Text.Trim(),
                    Prenom = txtPrenom.Text.Trim(),
                    RepresentantLegal = txtRepresentantLegal.Text.Trim(),
                    DateNaissance = dtpDateNaissance.Checked ? dtpDateNaissance.Value : (DateTime?)null,
                    NonArchive = chkNonArchive.Checked,
                    DateCreation = DateTime.Now,
                    DateModification = DateTime.Now
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnAnnuler_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtNom.Text))
            {
                MessageBox.Show("Le nom est obligatoire.", "Validation", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNom.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPrenom.Text))
            {
                MessageBox.Show("Le prénom est obligatoire.", "Validation", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPrenom.Focus();
                return false;
            }

            return true;
        }
    }
}
