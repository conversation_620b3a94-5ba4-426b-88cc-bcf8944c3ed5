using System;
using System.Drawing;
using System.Windows.Forms;
using GestionAssociations.Models;

namespace GestionAssociations.Forms.Association
{
    public partial class NouvelleAssembleeForm : Form
    {
        private DateTimePicker dtpDateAssemblee;
        private TextBox txtDescription;
        private TextBox txtObservations;
        private Button btnValider;
        private Button btnAnnuler;

        public AssembleeGenerale Assemblee { get; private set; }

        public NouvelleAssembleeForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "Nouvelle assemblée";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Panel principal avec titre
            var titlePanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(500, 40),
                BackColor = Color.FromArgb(70, 70, 100)
            };

            var titleLabel = new Label
            {
                Text = "Nouvelle assemblée",
                Location = new Point(15, 10),
                Size = new Size(200, 20),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            titlePanel.Controls.Add(titleLabel);

            // Panel de contenu
            var contentPanel = new Panel
            {
                Location = new Point(0, 40),
                Size = new Size(500, 270),
                BackColor = Color.White,
                Padding = new Padding(20)
            };

            // Date de l'assemblée
            var lblDate = new Label
            {
                Text = "Date de l'assemblée",
                Location = new Point(20, 20),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            dtpDateAssemblee = new DateTimePicker
            {
                Location = new Point(150, 17),
                Size = new Size(200, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today
            };

            // Description
            var lblDescription = new Label
            {
                Text = "Description",
                Location = new Point(20, 60),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            txtDescription = new TextBox
            {
                Location = new Point(150, 57),
                Size = new Size(300, 23),
                Text = "Assemblée générale"
            };

            // Observations
            var lblObservations = new Label
            {
                Text = "Observations",
                Location = new Point(20, 100),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9F)
            };

            txtObservations = new TextBox
            {
                Location = new Point(150, 97),
                Size = new Size(300, 80),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            // Boutons
            btnValider = new Button
            {
                Text = "Valider",
                Location = new Point(250, 200),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(70, 70, 100),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            btnAnnuler = new Button
            {
                Text = "Annuler",
                Location = new Point(360, 200),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(150, 150, 150),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };

            // Événements
            btnValider.Click += BtnValider_Click;
            btnAnnuler.Click += BtnAnnuler_Click;

            // Ajouter les contrôles au panel de contenu
            contentPanel.Controls.AddRange(new Control[]
            {
                lblDate, dtpDateAssemblee,
                lblDescription, txtDescription,
                lblObservations, txtObservations,
                btnValider, btnAnnuler
            });

            // Ajouter les panels au formulaire
            this.Controls.AddRange(new Control[] { titlePanel, contentPanel });
        }

        private void BtnValider_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                Assemblee = new AssembleeGenerale
                {
                    DateAssemblee = dtpDateAssemblee.Value,
                    Description = txtDescription.Text.Trim(),
                    Observations = txtObservations.Text.Trim(),
                    DateCreation = DateTime.Now,
                    DateModification = DateTime.Now
                };

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void BtnAnnuler_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtDescription.Text))
            {
                MessageBox.Show("La description est obligatoire.", "Validation", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDescription.Focus();
                return false;
            }

            return true;
        }
    }
}
