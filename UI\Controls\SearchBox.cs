using System;
using System.Drawing;
using System.Windows.Forms;
using GestionAssociations.UI.Themes;

namespace GestionAssociations.UI.Controls
{
    /// <summary>
    /// Contrôle de recherche avec icône et auto-complétion
    /// </summary>
    public class SearchBox : UserControl
    {
        private TextBox _textBox;
        private PictureBox _searchIcon;
        private Button _clearButton;
        private Timer _searchTimer;
        private string _placeholderText = "Rechercher...";
        private bool _isPlaceholderActive = true;

        public SearchBox()
        {
            InitializeComponent();
            ApplyStyle();
        }

        #region Propriétés

        /// <summary>
        /// Texte de recherche
        /// </summary>
        public override string Text
        {
            get { return _isPlaceholderActive ? string.Empty : _textBox.Text; }
            set
            {
                if (!string.IsNullOrEmpty(value))
                {
                    _isPlaceholderActive = false;
                    _textBox.Text = value;
                    _textBox.ForeColor = ThemeManager.Colors.TextPrimary;
                    UpdateClearButtonVisibility();
                }
                else
                {
                    ShowPlaceholder();
                }
            }
        }

        /// <summary>
        /// Texte du placeholder
        /// </summary>
        public string PlaceholderText
        {
            get { return _placeholderText; }
            set
            {
                _placeholderText = value;
                if (_isPlaceholderActive)
                {
                    _textBox.Text = _placeholderText;
                }
            }
        }

        /// <summary>
        /// Délai avant déclenchement de la recherche (en millisecondes)
        /// </summary>
        public int SearchDelay { get; set; } = 500;

        /// <summary>
        /// Active ou désactive l'auto-complétion
        /// </summary>
        public bool AutoCompleteEnabled
        {
            get { return _textBox.AutoCompleteMode != AutoCompleteMode.None; }
            set
            {
                _textBox.AutoCompleteMode = value ? AutoCompleteMode.SuggestAppend : AutoCompleteMode.None;
                _textBox.AutoCompleteSource = value ? AutoCompleteSource.CustomSource : AutoCompleteSource.None;
            }
        }

        /// <summary>
        /// Source des données d'auto-complétion
        /// </summary>
        public AutoCompleteStringCollection AutoCompleteSource
        {
            get { return _textBox.AutoCompleteCustomSource; }
            set { _textBox.AutoCompleteCustomSource = value; }
        }

        #endregion

        #region Événements

        /// <summary>
        /// Événement déclenché lors de la recherche
        /// </summary>
        public event EventHandler<SearchEventArgs> Search;

        /// <summary>
        /// Événement déclenché lors du changement de texte
        /// </summary>
        public event EventHandler TextChanged;

        /// <summary>
        /// Événement déclenché lors de l'effacement
        /// </summary>
        public event EventHandler Cleared;

        #endregion

        private void InitializeComponent()
        {
            Size = new Size(300, 32);
            BorderStyle = BorderStyle.FixedSingle;
            BackColor = Color.White;

            // Icône de recherche
            _searchIcon = new PictureBox
            {
                Size = new Size(16, 16),
                Location = new Point(8, 8),
                SizeMode = PictureBoxSizeMode.StretchImage,
                Image = CreateSearchIcon()
            };

            // TextBox principal
            _textBox = new TextBox
            {
                BorderStyle = BorderStyle.None,
                Location = new Point(30, 6),
                Size = new Size(240, 20),
                Font = ThemeManager.Fonts.Default,
                Text = _placeholderText,
                ForeColor = ThemeManager.Colors.TextSecondary
            };

            // Bouton d'effacement
            _clearButton = new Button
            {
                Size = new Size(20, 20),
                Location = new Point(275, 6),
                FlatStyle = FlatStyle.Flat,
                Text = "×",
                Font = new Font("Arial", 12F, FontStyle.Bold),
                ForeColor = ThemeManager.Colors.TextSecondary,
                BackColor = Color.Transparent,
                Visible = false,
                Cursor = Cursors.Hand
            };
            _clearButton.FlatAppearance.BorderSize = 0;

            // Timer pour la recherche différée
            _searchTimer = new Timer
            {
                Interval = SearchDelay
            };

            // Ajouter les contrôles
            Controls.AddRange(new Control[] { _searchIcon, _textBox, _clearButton });

            // Événements
            _textBox.TextChanged += TextBox_TextChanged;
            _textBox.Enter += TextBox_Enter;
            _textBox.Leave += TextBox_Leave;
            _textBox.KeyDown += TextBox_KeyDown;
            _clearButton.Click += ClearButton_Click;
            _searchTimer.Tick += SearchTimer_Tick;
        }

        private void ApplyStyle()
        {
            BackColor = Color.White;
            ForeColor = ThemeManager.Colors.TextPrimary;
        }

        private Bitmap CreateSearchIcon()
        {
            var bitmap = new Bitmap(16, 16);
            using (var g = Graphics.FromImage(bitmap))
            {
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                using (var pen = new Pen(ThemeManager.Colors.TextSecondary, 2))
                {
                    // Dessiner la loupe
                    g.DrawEllipse(pen, 2, 2, 8, 8);
                    g.DrawLine(pen, 9, 9, 14, 14);
                }
            }
            return bitmap;
        }

        private void TextBox_TextChanged(object sender, EventArgs e)
        {
            if (_isPlaceholderActive) return;

            UpdateClearButtonVisibility();
            
            // Redémarrer le timer pour la recherche différée
            _searchTimer.Stop();
            _searchTimer.Start();

            TextChanged?.Invoke(this, EventArgs.Empty);
        }

        private void TextBox_Enter(object sender, EventArgs e)
        {
            if (_isPlaceholderActive)
            {
                HidePlaceholder();
            }
        }

        private void TextBox_Leave(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(_textBox.Text))
            {
                ShowPlaceholder();
            }
        }

        private void TextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.SuppressKeyPress = true;
                PerformSearch();
            }
            else if (e.KeyCode == Keys.Escape)
            {
                Clear();
            }
        }

        private void ClearButton_Click(object sender, EventArgs e)
        {
            Clear();
        }

        private void SearchTimer_Tick(object sender, EventArgs e)
        {
            _searchTimer.Stop();
            PerformSearch();
        }

        private void ShowPlaceholder()
        {
            _isPlaceholderActive = true;
            _textBox.Text = _placeholderText;
            _textBox.ForeColor = ThemeManager.Colors.TextSecondary;
            UpdateClearButtonVisibility();
        }

        private void HidePlaceholder()
        {
            _isPlaceholderActive = false;
            _textBox.Text = string.Empty;
            _textBox.ForeColor = ThemeManager.Colors.TextPrimary;
        }

        private void UpdateClearButtonVisibility()
        {
            _clearButton.Visible = !_isPlaceholderActive && !string.IsNullOrEmpty(_textBox.Text);
        }

        private void PerformSearch()
        {
            var searchText = _isPlaceholderActive ? string.Empty : _textBox.Text;
            Search?.Invoke(this, new SearchEventArgs(searchText));
        }

        /// <summary>
        /// Efface le contenu de la recherche
        /// </summary>
        public void Clear()
        {
            ShowPlaceholder();
            Cleared?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Met le focus sur le contrôle de recherche
        /// </summary>
        public new void Focus()
        {
            _textBox.Focus();
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            
            if (_textBox != null && _clearButton != null)
            {
                _textBox.Size = new Size(Width - 70, _textBox.Height);
                _clearButton.Location = new Point(Width - 26, _clearButton.Location.Y);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _searchTimer?.Dispose();
                _searchIcon?.Image?.Dispose();
            }
            base.Dispose(disposing);
        }
    }

    /// <summary>
    /// Arguments pour l'événement de recherche
    /// </summary>
    public class SearchEventArgs : EventArgs
    {
        public string SearchText { get; }

        public SearchEventArgs(string searchText)
        {
            SearchText = searchText;
        }
    }
}
