using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using GestionAssociations.Data.Interfaces;
using GestionAssociations.Models;

namespace GestionAssociations.Data.Repositories
{
    /// <summary>
    /// Repository pour les pôles
    /// </summary>
    public class PoleRepository : BaseRepository<Pole>, IPoleRepository
    {
        public PoleRepository(IConnectionFactory connectionFactory) 
            : base(connectionFactory, "Poles")
        {
        }

        public override async Task<int> CreateAsync(Pole entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                INSERT INTO Poles (Libelle, Description, EstActif, DateCreation)
                VALUES (@Libelle, @Description, @EstActif, @DateCreation);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Pole entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                UPDATE Poles SET
                    Libelle = @Libelle,
                    Description = @Description,
                    EstActif = @EstActif,
                    DateModification = @DateModification
                WHERE Id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<IEnumerable<Pole>> GetActifsAsync()
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM Poles WHERE EstActif = 1 ORDER BY Libelle";
            return await connection.QueryAsync<Pole>(sql);
        }
    }

    /// <summary>
    /// Repository pour les sections
    /// </summary>
    public class SectionRepository : BaseRepository<Section>, ISectionRepository
    {
        public SectionRepository(IConnectionFactory connectionFactory) 
            : base(connectionFactory, "Sections")
        {
        }

        public override async Task<int> CreateAsync(Section entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                INSERT INTO Sections (Libelle, Description, Responsable, PoleId, EstActive, DateCreation)
                VALUES (@Libelle, @Description, @Responsable, @PoleId, @EstActive, @DateCreation);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Section entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                UPDATE Sections SET
                    Libelle = @Libelle,
                    Description = @Description,
                    Responsable = @Responsable,
                    PoleId = @PoleId,
                    EstActive = @EstActive,
                    DateModification = @DateModification
                WHERE Id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<IEnumerable<Section>> GetByPoleAsync(int poleId)
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM Sections WHERE PoleId = @PoleId ORDER BY Libelle";
            return await connection.QueryAsync<Section>(sql, new { PoleId = poleId });
        }

        public async Task<IEnumerable<Section>> GetActivesAsync()
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM Sections WHERE EstActive = 1 ORDER BY Libelle";
            return await connection.QueryAsync<Section>(sql);
        }
    }

    /// <summary>
    /// Repository pour les types d'adhérents
    /// </summary>
    public class TypeAdherentRepository : BaseRepository<TypeAdherent>, ITypeAdherentRepository
    {
        public TypeAdherentRepository(IConnectionFactory connectionFactory) 
            : base(connectionFactory, "TypesAdherents")
        {
        }

        public override async Task<int> CreateAsync(TypeAdherent entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                INSERT INTO TypesAdherents (Libelle, Description, TarifCotisation, EstActif, DateCreation)
                VALUES (@Libelle, @Description, @TarifCotisation, @EstActif, @DateCreation);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(TypeAdherent entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                UPDATE TypesAdherents SET
                    Libelle = @Libelle,
                    Description = @Description,
                    TarifCotisation = @TarifCotisation,
                    EstActif = @EstActif,
                    DateModification = @DateModification
                WHERE Id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<IEnumerable<TypeAdherent>> GetActifsAsync()
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM TypesAdherents WHERE EstActif = 1 ORDER BY Libelle";
            return await connection.QueryAsync<TypeAdherent>(sql);
        }
    }

    /// <summary>
    /// Repository pour les foyers
    /// </summary>
    public class FoyerRepository : BaseRepository<Foyer>, IFoyerRepository
    {
        public FoyerRepository(IConnectionFactory connectionFactory) 
            : base(connectionFactory, "Foyers")
        {
        }

        public override async Task<int> CreateAsync(Foyer entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                INSERT INTO Foyers (NomFoyer, AdressePrincipale, CodePostal, Ville, TelephonePrincipal, EmailPrincipal, DateCreation)
                VALUES (@NomFoyer, @AdressePrincipale, @CodePostal, @Ville, @TelephonePrincipal, @EmailPrincipal, @DateCreation);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Foyer entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                UPDATE Foyers SET
                    NomFoyer = @NomFoyer,
                    AdressePrincipale = @AdressePrincipale,
                    CodePostal = @CodePostal,
                    Ville = @Ville,
                    TelephonePrincipal = @TelephonePrincipal,
                    EmailPrincipal = @EmailPrincipal,
                    DateModification = @DateModification
                WHERE Id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<IEnumerable<Foyer>> GetActifsAsync()
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM Foyers ORDER BY NomFoyer";
            return await connection.QueryAsync<Foyer>(sql);
        }
    }

    /// <summary>
    /// Repository pour les médecins
    /// </summary>
    public class MedecinRepository : BaseRepository<Medecin>, IMedecinRepository
    {
        public MedecinRepository(IConnectionFactory connectionFactory) 
            : base(connectionFactory, "Medecins")
        {
        }

        public override async Task<int> CreateAsync(Medecin entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                INSERT INTO Medecins (NomPrenom, ComplementAdresse, NumeroVoie, NomVoie, CodePostal, Ville, 
                                    Telephone, Mobile, NumeroOrdre, Fax, EstActif, DateCreation)
                VALUES (@NomPrenom, @ComplementAdresse, @NumeroVoie, @NomVoie, @CodePostal, @Ville,
                        @Telephone, @Mobile, @NumeroOrdre, @Fax, @EstActif, @DateCreation);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Medecin entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                UPDATE Medecins SET
                    NomPrenom = @NomPrenom,
                    ComplementAdresse = @ComplementAdresse,
                    NumeroVoie = @NumeroVoie,
                    NomVoie = @NomVoie,
                    CodePostal = @CodePostal,
                    Ville = @Ville,
                    Telephone = @Telephone,
                    Mobile = @Mobile,
                    NumeroOrdre = @NumeroOrdre,
                    Fax = @Fax,
                    EstActif = @EstActif,
                    DateModification = @DateModification
                WHERE Id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<IEnumerable<Medecin>> GetActifsAsync()
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM Medecins WHERE EstActif = 1 ORDER BY NomPrenom";
            return await connection.QueryAsync<Medecin>(sql);
        }

        public async Task<IEnumerable<Medecin>> SearchAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetActifsAsync();

            using var connection = CreateConnection();
            var sql = @"
                SELECT * FROM Medecins 
                WHERE EstActif = 1 AND NomPrenom LIKE @SearchTerm
                ORDER BY NomPrenom";
            return await connection.QueryAsync<Medecin>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }
    }

    /// <summary>
    /// Repository pour les personnes à prévenir
    /// </summary>
    public class PersonneAPrevenirRepository : BaseRepository<PersonneAPrevenir>, IPersonneAPrevenirRepository
    {
        public PersonneAPrevenirRepository(IConnectionFactory connectionFactory) 
            : base(connectionFactory, "PersonnesAPrevenir")
        {
        }

        public override async Task<int> CreateAsync(PersonneAPrevenir entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                INSERT INTO PersonnesAPrevenir (AdherentId, NomPrenom, TelDomicile, TelBureau, TelPortable, Email, DateCreation)
                VALUES (@AdherentId, @NomPrenom, @TelDomicile, @TelBureau, @TelPortable, @Email, @DateCreation);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(PersonneAPrevenir entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                UPDATE PersonnesAPrevenir SET
                    NomPrenom = @NomPrenom,
                    TelDomicile = @TelDomicile,
                    TelBureau = @TelBureau,
                    TelPortable = @TelPortable,
                    Email = @Email,
                    DateModification = @DateModification
                WHERE Id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<IEnumerable<PersonneAPrevenir>> GetByAdherentAsync(int adherentId)
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM PersonnesAPrevenir WHERE AdherentId = @AdherentId ORDER BY NomPrenom";
            return await connection.QueryAsync<PersonneAPrevenir>(sql, new { AdherentId = adherentId });
        }
    }

    /// <summary>
    /// Repository pour les cotisations d'adhérents
    /// </summary>
    public class CotisationAdherentRepository : BaseRepository<CotisationAdherent>, ICotisationAdherentRepository
    {
        public CotisationAdherentRepository(IConnectionFactory connectionFactory) 
            : base(connectionFactory, "CotisationsAdherents")
        {
        }

        public override async Task<int> CreateAsync(CotisationAdherent entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                INSERT INTO CotisationsAdherents (AdherentId, Periode, Libelle, Montant, DatePaiement, ModePaiement, DateCreation)
                VALUES (@AdherentId, @Periode, @Libelle, @Montant, @DatePaiement, @ModePaiement, @DateCreation);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(CotisationAdherent entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                UPDATE CotisationsAdherents SET
                    Periode = @Periode,
                    Libelle = @Libelle,
                    Montant = @Montant,
                    DatePaiement = @DatePaiement,
                    ModePaiement = @ModePaiement,
                    DateModification = @DateModification
                WHERE Id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<IEnumerable<CotisationAdherent>> GetByAdherentAsync(int adherentId)
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM CotisationsAdherents WHERE AdherentId = @AdherentId ORDER BY DateCreation DESC";
            return await connection.QueryAsync<CotisationAdherent>(sql, new { AdherentId = adherentId });
        }

        public async Task<IEnumerable<CotisationAdherent>> GetByPeriodeAsync(string periode)
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM CotisationsAdherents WHERE Periode = @Periode ORDER BY DateCreation DESC";
            return await connection.QueryAsync<CotisationAdherent>(sql, new { Periode = periode });
        }
    }

    /// <summary>
    /// Repository pour les fichiers d'adhérents
    /// </summary>
    public class FichierAdherentRepository : BaseRepository<FichierAdherent>, IFichierAdherentRepository
    {
        public FichierAdherentRepository(IConnectionFactory connectionFactory) 
            : base(connectionFactory, "FichiersAdherents")
        {
        }

        public override async Task<int> CreateAsync(FichierAdherent entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                INSERT INTO FichiersAdherents (AdherentId, NomDocument, CheminFichier, DateAjout, TailleFichier, TypeMime)
                VALUES (@AdherentId, @NomDocument, @CheminFichier, @DateAjout, @TailleFichier, @TypeMime);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(FichierAdherent entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                UPDATE FichiersAdherents SET
                    NomDocument = @NomDocument,
                    CheminFichier = @CheminFichier,
                    TailleFichier = @TailleFichier,
                    TypeMime = @TypeMime
                WHERE Id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<IEnumerable<FichierAdherent>> GetByAdherentAsync(int adherentId)
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM FichiersAdherents WHERE AdherentId = @AdherentId ORDER BY DateAjout DESC";
            return await connection.QueryAsync<FichierAdherent>(sql, new { AdherentId = adherentId });
        }
    }

    /// <summary>
    /// Repository pour les emails d'adhérents
    /// </summary>
    public class EmailAdherentRepository : BaseRepository<EmailAdherent>, IEmailAdherentRepository
    {
        public EmailAdherentRepository(IConnectionFactory connectionFactory) 
            : base(connectionFactory, "EmailsAdherents")
        {
        }

        public override async Task<int> CreateAsync(EmailAdherent entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                INSERT INTO EmailsAdherents (AdherentId, DateEnvoi, Sujet, Corps, PieceJointe, StatutEnvoi)
                VALUES (@AdherentId, @DateEnvoi, @Sujet, @Corps, @PieceJointe, @StatutEnvoi);
                SELECT CAST(SCOPE_IDENTITY() as int);";
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(EmailAdherent entity)
        {
            using var connection = CreateConnection();
            var sql = @"
                UPDATE EmailsAdherents SET
                    Sujet = @Sujet,
                    Corps = @Corps,
                    PieceJointe = @PieceJointe,
                    StatutEnvoi = @StatutEnvoi
                WHERE Id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<IEnumerable<EmailAdherent>> GetByAdherentAsync(int adherentId)
        {
            using var connection = CreateConnection();
            var sql = "SELECT * FROM EmailsAdherents WHERE AdherentId = @AdherentId ORDER BY DateEnvoi DESC";
            return await connection.QueryAsync<EmailAdherent>(sql, new { AdherentId = adherentId });
        }
    }
}
