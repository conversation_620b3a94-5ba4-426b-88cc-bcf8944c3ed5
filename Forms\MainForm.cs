using System;
using System.Drawing;
using System.Windows.Forms;
using GestionAssociations.Forms.Association;

namespace GestionAssociations.Forms
{
    public partial class MainForm : Form
    {
        private MenuStrip menuStrip;
        private StatusStrip statusStrip;
        private Panel contentPanel;

        public MainForm()
        {
            InitializeComponent();
            InitializeMenu();
            InitializeStatusBar();
            InitializeContentPanel();
        }

        private void InitializeMenu()
        {
            menuStrip = new MenuStrip();
            menuStrip.BackColor = Color.FromArgb(70, 70, 100);
            menuStrip.ForeColor = Color.White;
            menuStrip.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Menu ASSOCIATION
            var associationMenu = new ToolStripMenuItem("ASSOCIATION");
            associationMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Accueil", null, (s, e) => ShowForm("Accueil")),
                new ToolStripMenuItem("Tableau de bord", null, (s, e) => ShowForm("TableauDeBord")),
                new ToolStripMenuItem("Stockage sur serveur", null, (s, e) => ShowForm("StockageServeur")),
                new ToolStripMenuItem("Description association", null, (s, e) => ShowForm("DescriptionAssociation")),
                new ToolStripMenuItem("Gestion des pôles", null, (s, e) => ShowForm("GestionPoles")),
                new ToolStripMenuItem("Membres du bureau", null, (s, e) => ShowForm("MembresBureau")),
                new ToolStripMenuItem("Signatures", null, (s, e) => ShowForm("Signatures")),
                new ToolStripMenuItem("Droits utilisateurs", null, (s, e) => ShowForm("DroitsUtilisateurs")),
                new ToolStripMenuItem("Modifier mot de passe", null, (s, e) => ShowForm("ModifierMotDePasse"))
            });

            // Menu ADHÉRENTS
            var adherentsMenu = new ToolStripMenuItem("ADHÉRENTS");
            adherentsMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Adhérents", null, (s, e) => ShowForm("Adherents")),
                new ToolStripMenuItem("Listes de diffusion", null, (s, e) => ShowForm("ListesDiffusion")),
                new ToolStripMenuItem("Types d'adhérents", null, (s, e) => ShowForm("TypesAdherents")),
                new ToolStripMenuItem("Personnaliser", null, (s, e) => ShowForm("PersonnaliserAdherents")),
                new ToolStripMenuItem("Stats adhérents", null, (s, e) => ShowForm("StatsAdherents")),
                new ToolStripMenuItem("Import", null, (s, e) => ShowForm("ImportAdherents")),
                new ToolStripMenuItem("Export", null, (s, e) => ShowForm("ExportAdherents")),
                new ToolStripMenuItem("Sections", null, (s, e) => ShowForm("Sections")),
                new ToolStripMenuItem("Stats sections", null, (s, e) => ShowForm("StatsSections")),
                new ToolStripMenuItem("Cotisations", null, (s, e) => ShowForm("Cotisations")),
                new ToolStripMenuItem("Pièces justificatives", null, (s, e) => ShowForm("PiecesJustificatives")),
                new ToolStripMenuItem("Certificats médicaux", null, (s, e) => ShowForm("CertificatsMedicaux")),
                new ToolStripMenuItem("Foyers", null, (s, e) => ShowForm("Foyers"))
            });

            // Menu COMPTABILITÉ
            var comptabiliteMenu = new ToolStripMenuItem("COMPTABILITÉ");
            comptabiliteMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Relevé de compte", null, (s, e) => ShowForm("ReleveCompte")),
                new ToolStripMenuItem("Comptes bancaires", null, (s, e) => ShowForm("ComptesBancaires")),
                new ToolStripMenuItem("Chéquiers", null, (s, e) => ShowForm("Chequiers")),
                new ToolStripMenuItem("Échéancier", null, (s, e) => ShowForm("Echeancier")),
                new ToolStripMenuItem("Plan comptable", null, (s, e) => ShowForm("PlanComptable")),
                new ToolStripMenuItem("Tiers", null, (s, e) => ShowForm("Tiers")),
                new ToolStripMenuItem("Événements", null, (s, e) => ShowForm("EvenementsComptables")),
                new ToolStripMenuItem("Import", null, (s, e) => ShowForm("ImportComptabilite")),
                new ToolStripMenuItem("Compte résultat", null, (s, e) => ShowForm("CompteResultat")),
                new ToolStripMenuItem("Bilan par catégorie", null, (s, e) => ShowForm("BilanCategorie")),
                new ToolStripMenuItem("Bilan par événement", null, (s, e) => ShowForm("BilanEvenement")),
                new ToolStripMenuItem("Budgets", null, (s, e) => ShowForm("Budgets")),
                new ToolStripMenuItem("Dons", null, (s, e) => ShowForm("Dons"))
            });

            // Menu ORGANISER
            var organiserMenu = new ToolStripMenuItem("ORGANISER");
            organiserMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Activités", null, (s, e) => ShowForm("Activites")),
                new ToolStripMenuItem("Inscriptions activités", null, (s, e) => ShowForm("InscriptionsActivites")),
                new ToolStripMenuItem("Participants activités", null, (s, e) => ShowForm("ParticipantsActivites")),
                new ToolStripMenuItem("Manifestation", null, (s, e) => ShowForm("Manifestation")),
                new ToolStripMenuItem("Inscriptions manifestation", null, (s, e) => ShowForm("InscriptionsManifestation")),
                new ToolStripMenuItem("Participants manifestation", null, (s, e) => ShowForm("ParticipantsManifestation")),
                new ToolStripMenuItem("Plan type", null, (s, e) => ShowForm("PlanType")),
                new ToolStripMenuItem("Personnaliser", null, (s, e) => ShowForm("PersonnaliserOrganiser")),
                new ToolStripMenuItem("Suivi des avoirs", null, (s, e) => ShowForm("SuiviAvoirs"))
            });

            // Menu SECRÉTARIAT
            var secretariatMenu = new ToolStripMenuItem("SECRÉTARIAT");
            secretariatMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Courriers", null, (s, e) => ShowForm("Courriers")),
                new ToolStripMenuItem("Étiquettes", null, (s, e) => ShowForm("Etiquettes")),
                new ToolStripMenuItem("Publipostage", null, (s, e) => ShowForm("Publipostage")),
                new ToolStripMenuItem("Contacts", null, (s, e) => ShowForm("Contacts")),
                new ToolStripMenuItem("Import contacts", null, (s, e) => ShowForm("ImportContacts")),
                new ToolStripMenuItem("Emails", null, (s, e) => ShowForm("Emails")),
                new ToolStripMenuItem("Modèles emails", null, (s, e) => ShowForm("ModelesEmails")),
                new ToolStripMenuItem("SMS", null, (s, e) => ShowForm("SMS")),
                new ToolStripMenuItem("Bons d'achat", null, (s, e) => ShowForm("BonsAchat")),
                new ToolStripMenuItem("Édition hors statuts", null, (s, e) => ShowForm("EditionHorsStatuts")),
                new ToolStripMenuItem("Rembourser hors statuts", null, (s, e) => ShowForm("RembourserHorsStatuts")),
                new ToolStripMenuItem("Stats hors statuts", null, (s, e) => ShowForm("StatsHorsStatuts"))
            });

            // Menu MATÉRIELS
            var materielsMenu = new ToolStripMenuItem("MATÉRIELS");
            materielsMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("Gestion matériel", null, (s, e) => ShowForm("GestionMateriel")),
                new ToolStripMenuItem("Entrées", null, (s, e) => ShowForm("EntreesMateriel")),
                new ToolStripMenuItem("Sorties", null, (s, e) => ShowForm("SortiesMateriel")),
                new ToolStripMenuItem("Suivi des PBs", null, (s, e) => ShowForm("SuiviProblemes")),
                new ToolStripMenuItem("Personnaliser", null, (s, e) => ShowForm("PersonnaliserMateriels")),
                new ToolStripMenuItem("Stats matériels", null, (s, e) => ShowForm("StatsMateriels")),
                new ToolStripMenuItem("Import matériels", null, (s, e) => ShowForm("ImportMateriels"))
            });

            menuStrip.Items.AddRange(new ToolStripItem[]
            {
                associationMenu,
                adherentsMenu,
                comptabiliteMenu,
                organiserMenu,
                secretariatMenu,
                materielsMenu
            });

            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void InitializeStatusBar()
        {
            statusStrip = new StatusStrip();
            statusStrip.BackColor = Color.FromArgb(240, 240, 240);
            
            var statusLabel = new ToolStripStatusLabel("Prêt");
            statusStrip.Items.Add(statusLabel);
            
            this.Controls.Add(statusStrip);
        }

        private void InitializeContentPanel()
        {
            contentPanel = new Panel();
            contentPanel.Dock = DockStyle.Fill;
            contentPanel.BackColor = Color.White;
            this.Controls.Add(contentPanel);
        }

        private void ShowForm(string formName)
        {
            try
            {
                Form formToShow = null;

                switch (formName)
                {
                    case "DescriptionAssociation":
                        formToShow = new DescriptionAssociationForm();
                        break;
                    case "MembresBureau":
                        formToShow = new MembresBureauForm();
                        break;
                    case "DroitsUtilisateurs":
                        formToShow = new GestionUtilisateursForm();
                        break;
                    case "TableauDeBord":
                        // TODO: Implémenter le tableau de bord
                        MessageBox.Show("Tableau de bord - À implémenter", "Information");
                        return;
                    case "StockageServeur":
                        // TODO: Implémenter le stockage serveur
                        MessageBox.Show("Stockage serveur - À implémenter", "Information");
                        return;
                    case "GestionPoles":
                        // TODO: Implémenter la gestion des pôles
                        MessageBox.Show("Gestion des pôles - À implémenter", "Information");
                        return;
                    case "Signatures":
                        // TODO: Implémenter les signatures
                        MessageBox.Show("Signatures - À implémenter", "Information");
                        return;
                    case "ModifierMotDePasse":
                        // TODO: Implémenter la modification de mot de passe
                        MessageBox.Show("Modifier mot de passe - À implémenter", "Information");
                        return;
                    default:
                        MessageBox.Show($"Formulaire '{formName}' non encore implémenté", "Information",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                }

                if (formToShow != null)
                {
                    // Ouvrir le formulaire en modal
                    formToShow.ShowDialog(this);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'ouverture du formulaire: {ex.Message}", "Erreur",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
