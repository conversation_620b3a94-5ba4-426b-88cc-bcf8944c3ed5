using System;
using System.Drawing;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace GestionAssociations.UI.Controls
{
    /// <summary>
    /// TextBox avec validation intégrée et style moderne
    /// </summary>
    public class ValidatedTextBox : TextBox
    {
        private string _validationPattern;
        private string _errorMessage;
        private bool _isValid = true;
        private Label _errorLabel;
        private Color _originalBackColor;
        private Color _originalBorderColor;

        public ValidatedTextBox()
        {
            InitializeComponent();
            ApplyModernStyle();
        }

        #region Propriétés

        /// <summary>
        /// Pattern de validation (expression régulière)
        /// </summary>
        public string ValidationPattern
        {
            get { return _validationPattern; }
            set { _validationPattern = value; }
        }

        /// <summary>
        /// Message d'erreur à afficher en cas de validation échouée
        /// </summary>
        public string ErrorMessage
        {
            get { return _errorMessage; }
            set { _errorMessage = value; }
        }

        /// <summary>
        /// Indique si la valeur actuelle est valide
        /// </summary>
        public bool IsValid
        {
            get { return _isValid; }
            private set
            {
                if (_isValid != value)
                {
                    _isValid = value;
                    UpdateValidationStyle();
                    OnValidationChanged();
                }
            }
        }

        /// <summary>
        /// Validation obligatoire
        /// </summary>
        public bool Required { get; set; }

        /// <summary>
        /// Longueur minimale
        /// </summary>
        public int MinLength { get; set; }

        /// <summary>
        /// Longueur maximale
        /// </summary>
        public int MaxLength { get; set; } = 32767;

        /// <summary>
        /// Type de validation prédéfini
        /// </summary>
        public ValidationType ValidationType { get; set; } = ValidationType.None;

        #endregion

        #region Événements

        /// <summary>
        /// Événement déclenché quand l'état de validation change
        /// </summary>
        public event EventHandler ValidationChanged;

        protected virtual void OnValidationChanged()
        {
            ValidationChanged?.Invoke(this, EventArgs.Empty);
        }

        #endregion

        private void InitializeComponent()
        {
            _originalBackColor = BackColor;
            
            // Créer le label d'erreur
            _errorLabel = new Label
            {
                ForeColor = Color.FromArgb(220, 53, 69),
                Font = new Font("Segoe UI", 8F),
                AutoSize = true,
                Visible = false
            };

            // Ajouter les événements
            TextChanged += ValidatedTextBox_TextChanged;
            Leave += ValidatedTextBox_Leave;
            Enter += ValidatedTextBox_Enter;
        }

        private void ApplyModernStyle()
        {
            Font = new Font("Segoe UI", 9F);
            BorderStyle = BorderStyle.FixedSingle;
            BackColor = Color.White;
            ForeColor = Color.FromArgb(64, 64, 64);
            
            // Padding interne simulé
            Padding = new Padding(5);
        }

        protected override void OnParentChanged(EventArgs e)
        {
            base.OnParentChanged(e);
            
            if (Parent != null && _errorLabel != null)
            {
                Parent.Controls.Add(_errorLabel);
                PositionErrorLabel();
            }
        }

        protected override void OnLocationChanged(EventArgs e)
        {
            base.OnLocationChanged(e);
            PositionErrorLabel();
        }

        private void PositionErrorLabel()
        {
            if (_errorLabel != null && Parent != null)
            {
                _errorLabel.Location = new Point(Left, Bottom + 2);
            }
        }

        private void ValidatedTextBox_TextChanged(object sender, EventArgs e)
        {
            ValidateInput();
        }

        private void ValidatedTextBox_Leave(object sender, EventArgs e)
        {
            ValidateInput();
        }

        private void ValidatedTextBox_Enter(object sender, EventArgs e)
        {
            BackColor = Color.FromArgb(248, 249, 250);
        }

        /// <summary>
        /// Valide l'entrée actuelle
        /// </summary>
        /// <returns>True si valide</returns>
        public bool ValidateInput()
        {
            var text = Text?.Trim() ?? string.Empty;
            var errors = new System.Collections.Generic.List<string>();

            // Validation obligatoire
            if (Required && string.IsNullOrEmpty(text))
            {
                errors.Add("Ce champ est obligatoire");
            }

            // Validation de longueur
            if (!string.IsNullOrEmpty(text))
            {
                if (MinLength > 0 && text.Length < MinLength)
                {
                    errors.Add($"Minimum {MinLength} caractères requis");
                }

                if (MaxLength > 0 && text.Length > MaxLength)
                {
                    errors.Add($"Maximum {MaxLength} caractères autorisés");
                }
            }

            // Validation par type prédéfini
            if (!string.IsNullOrEmpty(text) && ValidationType != ValidationType.None)
            {
                if (!ValidateByType(text))
                {
                    errors.Add(GetValidationTypeErrorMessage());
                }
            }

            // Validation par pattern personnalisé
            if (!string.IsNullOrEmpty(text) && !string.IsNullOrEmpty(ValidationPattern))
            {
                if (!Regex.IsMatch(text, ValidationPattern))
                {
                    errors.Add(ErrorMessage ?? "Format invalide");
                }
            }

            IsValid = errors.Count == 0;
            
            if (!IsValid)
            {
                ShowError(string.Join(", ", errors));
            }
            else
            {
                HideError();
            }

            return IsValid;
        }

        private bool ValidateByType(string text)
        {
            switch (ValidationType)
            {
                case ValidationType.Email:
                    return Regex.IsMatch(text, @"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                
                case ValidationType.Phone:
                    return Regex.IsMatch(text, @"^[\d\s\-\.\(\)\+]+$");
                
                case ValidationType.PostalCode:
                    return Regex.IsMatch(text, @"^\d{5}$");
                
                case ValidationType.Numeric:
                    return decimal.TryParse(text, out _);
                
                case ValidationType.Integer:
                    return int.TryParse(text, out _);
                
                case ValidationType.AlphaOnly:
                    return Regex.IsMatch(text, @"^[a-zA-ZÀ-ÿ\s]+$");
                
                case ValidationType.AlphaNumeric:
                    return Regex.IsMatch(text, @"^[a-zA-Z0-9À-ÿ\s]+$");
                
                default:
                    return true;
            }
        }

        private string GetValidationTypeErrorMessage()
        {
            switch (ValidationType)
            {
                case ValidationType.Email:
                    return "Format d'email invalide";
                case ValidationType.Phone:
                    return "Format de téléphone invalide";
                case ValidationType.PostalCode:
                    return "Code postal invalide (5 chiffres)";
                case ValidationType.Numeric:
                    return "Valeur numérique requise";
                case ValidationType.Integer:
                    return "Nombre entier requis";
                case ValidationType.AlphaOnly:
                    return "Lettres uniquement";
                case ValidationType.AlphaNumeric:
                    return "Lettres et chiffres uniquement";
                default:
                    return "Format invalide";
            }
        }

        private void UpdateValidationStyle()
        {
            if (IsValid)
            {
                BackColor = _originalBackColor;
                ForeColor = Color.FromArgb(64, 64, 64);
            }
            else
            {
                BackColor = Color.FromArgb(255, 245, 245);
                ForeColor = Color.FromArgb(220, 53, 69);
            }
        }

        private void ShowError(string message)
        {
            if (_errorLabel != null)
            {
                _errorLabel.Text = message;
                _errorLabel.Visible = true;
            }
        }

        private void HideError()
        {
            if (_errorLabel != null)
            {
                _errorLabel.Visible = false;
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing && _errorLabel != null)
            {
                _errorLabel.Dispose();
            }
            base.Dispose(disposing);
        }
    }

    /// <summary>
    /// Types de validation prédéfinis
    /// </summary>
    public enum ValidationType
    {
        None,
        Email,
        Phone,
        PostalCode,
        Numeric,
        Integer,
        AlphaOnly,
        AlphaNumeric
    }
}
