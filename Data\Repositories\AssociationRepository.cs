using System.Threading.Tasks;
using GestionAssociations.Models;

namespace GestionAssociations.Data.Repositories
{
    public interface IAssociationRepository : IRepository<Association>
    {
        Task<Association> GetAssociationPrincipaleAsync();
        Task<President> GetPresidentAsync(int associationId);
        Task<Tresorier> GetTresorierAsync(int associationId);
        Task<Secretaire> GetSecretaireAsync(int associationId);
        Task<bool> UpdatePresidentAsync(President president);
        Task<bool> UpdateTresorierAsync(Tresorier tresorier);
        Task<bool> UpdateSecretaireAsync(Secretaire secretaire);
    }

    public class AssociationRepository : BaseRepository<Association>, IAssociationRepository
    {
        public AssociationRepository(IConnectionFactory connectionFactory)
            : base(connectionFactory, "Associations")
        {
        }

        public override async Task<int> CreateAsync(Association entity)
        {
            var sql = @"
                INSERT INTO Associations (
                    Nom, SuiteDuNom, ComplementAdresse, NumeroEtNomVoie, BpOuLieuDit,
                    CodePostal, Commune, Pays, Devise, Telephone, Fax, Email, SiteInternet,
                    ButAssociation, ReferencePrefecture, AgrementPrefecture, NumeroIntraCommunitaire,
                    RegistreCommerce, CodeNaf, Siret, Banque, Agence, Etablissement,
                    CodeGuichet, NumeroCompte, CleRib, Iban, Bic, IdentifiantSepa,
                    Logo, LogoHauteur, LogoLargeur, GestionParPoles, DateCreation, DateModification
                ) VALUES (
                    @Nom, @SuiteDuNom, @ComplementAdresse, @NumeroEtNomVoie, @BpOuLieuDit,
                    @CodePostal, @Commune, @Pays, @Devise, @Telephone, @Fax, @Email, @SiteInternet,
                    @ButAssociation, @ReferencePrefecture, @AgrementPrefecture, @NumeroIntraCommunitaire,
                    @RegistreCommerce, @CodeNaf, @Siret, @Banque, @Agence, @Etablissement,
                    @CodeGuichet, @NumeroCompte, @CleRib, @Iban, @Bic, @IdentifiantSepa,
                    @Logo, @LogoHauteur, @LogoLargeur, @GestionParPoles, @DateCreation, @DateModification
                );
                SELECT CAST(SCOPE_IDENTITY() as int);";

            return await ExecuteInsertAsync(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Association entity)
        {
            var sql = @"
                UPDATE Associations SET
                    Nom = @Nom, SuiteDuNom = @SuiteDuNom, ComplementAdresse = @ComplementAdresse,
                    NumeroEtNomVoie = @NumeroEtNomVoie, BpOuLieuDit = @BpOuLieuDit,
                    CodePostal = @CodePostal, Commune = @Commune, Pays = @Pays, Devise = @Devise,
                    Telephone = @Telephone, Fax = @Fax, Email = @Email, SiteInternet = @SiteInternet,
                    ButAssociation = @ButAssociation, ReferencePrefecture = @ReferencePrefecture,
                    AgrementPrefecture = @AgrementPrefecture, NumeroIntraCommunitaire = @NumeroIntraCommunitaire,
                    RegistreCommerce = @RegistreCommerce, CodeNaf = @CodeNaf, Siret = @Siret,
                    Banque = @Banque, Agence = @Agence, Etablissement = @Etablissement,
                    CodeGuichet = @CodeGuichet, NumeroCompte = @NumeroCompte, CleRib = @CleRib,
                    Iban = @Iban, Bic = @Bic, IdentifiantSepa = @IdentifiantSepa,
                    Logo = @Logo, LogoHauteur = @LogoHauteur, LogoLargeur = @LogoLargeur,
                    GestionParPoles = @GestionParPoles, DateModification = @DateModification
                WHERE Id = @Id";

            return await ExecuteUpdateAsync(sql, entity);
        }

        public async Task<Association> GetAssociationPrincipaleAsync()
        {
            var sql = "SELECT TOP 1 * FROM Associations ORDER BY Id";
            return await QueryFirstOrDefaultAsync(sql);
        }

        public async Task<President> GetPresidentAsync(int associationId)
        {
            var sql = "SELECT * FROM Presidents WHERE AssociationId = @AssociationId";
            return await QueryFirstOrDefaultAsync<President>(sql, new { AssociationId = associationId });
        }

        public async Task<Tresorier> GetTresorierAsync(int associationId)
        {
            var sql = "SELECT * FROM Tresoriers WHERE AssociationId = @AssociationId";
            return await QueryFirstOrDefaultAsync<Tresorier>(sql, new { AssociationId = associationId });
        }

        public async Task<Secretaire> GetSecretaireAsync(int associationId)
        {
            var sql = "SELECT * FROM Secretaires WHERE AssociationId = @AssociationId";
            return await QueryFirstOrDefaultAsync<Secretaire>(sql, new { AssociationId = associationId });
        }

        public async Task<bool> UpdatePresidentAsync(President president)
        {
            var sql = @"
                UPDATE Presidents SET
                    NomPresident = @NomPresident, ComplementAdresse = @ComplementAdresse,
                    Adresse = @Adresse, BpOuLieuDit = @BpOuLieuDit, CodePostal = @CodePostal,
                    Commune = @Commune, Telephone = @Telephone, Portable = @Portable, Email = @Email
                WHERE AssociationId = @AssociationId";

            return await ExecuteUpdateAsync(sql, president);
        }

        public async Task<bool> UpdateTresorierAsync(Tresorier tresorier)
        {
            var sql = @"
                UPDATE Tresoriers SET
                    NomTresorier = @NomTresorier, ComplementAdresse = @ComplementAdresse,
                    Adresse = @Adresse, BpOuLieuDit = @BpOuLieuDit, CodePostal = @CodePostal,
                    Commune = @Commune, Telephone = @Telephone, Portable = @Portable, Email = @Email
                WHERE AssociationId = @AssociationId";

            return await ExecuteUpdateAsync(sql, tresorier);
        }

        public async Task<bool> UpdateSecretaireAsync(Secretaire secretaire)
        {
            var sql = @"
                UPDATE Secretaires SET
                    NomSecretaire = @NomSecretaire, ComplementAdresse = @ComplementAdresse,
                    Adresse = @Adresse, BpOuLieuDit = @BpOuLieuDit, CodePostal = @CodePostal,
                    Commune = @Commune, Telephone = @Telephone, Portable = @Portable, Email = @Email
                WHERE AssociationId = @AssociationId";

            return await ExecuteUpdateAsync(sql, secretaire);
        }

        private async Task<T> QueryFirstOrDefaultAsync<T>(string sql, object parameters = null)
        {
            using (var connection = CreateConnection())
            {
                return await connection.QueryFirstOrDefaultAsync<T>(sql, parameters);
            }
        }
    }
}
