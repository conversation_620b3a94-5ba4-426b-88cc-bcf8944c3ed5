using System;
using System.Threading.Tasks;
using GestionAssociations.Data.Repositories;
using GestionAssociations.Models;

namespace GestionAssociations.Services
{
    public interface IAssociationService
    {
        Task<Association> GetAssociationPrincipaleAsync();
        Task<bool> UpdateAssociationAsync(Association association);
        Task<President> GetPresidentAsync();
        Task<Tresorier> GetTresorierAsync();
        Task<Secretaire> GetSecretaireAsync();
        Task<bool> UpdatePresidentAsync(President president);
        Task<bool> UpdateTresorierAsync(Tresorier tresorier);
        Task<bool> UpdateSecretaireAsync(Secretaire secretaire);
        Task<bool> UpdateLogoAsync(byte[] logoData, int hauteur, int largeur);
    }

    public class AssociationService : IAssociationService
    {
        private readonly IAssociationRepository _associationRepository;

        public AssociationService(IAssociationRepository associationRepository)
        {
            _associationRepository = associationRepository;
        }

        public AssociationService() : this(new AssociationRepository())
        {
        }

        public async Task<Association> GetAssociationPrincipaleAsync()
        {
            return await _associationRepository.GetAssociationPrincipaleAsync();
        }

        public async Task<bool> UpdateAssociationAsync(Association association)
        {
            association.DateModification = DateTime.Now;
            return await _associationRepository.UpdateAsync(association);
        }

        public async Task<President> GetPresidentAsync()
        {
            var association = await GetAssociationPrincipaleAsync();
            if (association == null) return null;

            return await _associationRepository.GetPresidentAsync(association.Id);
        }

        public async Task<Tresorier> GetTresorierAsync()
        {
            var association = await GetAssociationPrincipaleAsync();
            if (association == null) return null;

            return await _associationRepository.GetTresorierAsync(association.Id);
        }

        public async Task<Secretaire> GetSecretaireAsync()
        {
            var association = await GetAssociationPrincipaleAsync();
            if (association == null) return null;

            return await _associationRepository.GetSecretaireAsync(association.Id);
        }

        public async Task<bool> UpdatePresidentAsync(President president)
        {
            return await _associationRepository.UpdatePresidentAsync(president);
        }

        public async Task<bool> UpdateTresorierAsync(Tresorier tresorier)
        {
            return await _associationRepository.UpdateTresorierAsync(tresorier);
        }

        public async Task<bool> UpdateSecretaireAsync(Secretaire secretaire)
        {
            return await _associationRepository.UpdateSecretaireAsync(secretaire);
        }

        public async Task<bool> UpdateLogoAsync(byte[] logoData, int hauteur, int largeur)
        {
            var association = await GetAssociationPrincipaleAsync();
            if (association == null) return false;

            association.Logo = logoData;
            association.LogoHauteur = hauteur;
            association.LogoLargeur = largeur;
            association.DateModification = DateTime.Now;

            return await _associationRepository.UpdateAsync(association);
        }
    }
}
