using System;

namespace GestionAssociations.Models
{
    public class Adherent
    {
        public int Id { get; set; }
        public string NumeroAdherent { get; set; }
        public string Civilite { get; set; }
        public string Nom { get; set; }
        public string Prenom { get; set; }
        public DateTime? DateNaissance { get; set; }
        public string LieuNaissance { get; set; }
        public string Nationalite { get; set; }
        public string Adresse { get; set; }
        public string ComplementAdresse { get; set; }
        public string CodePostal { get; set; }
        public string Ville { get; set; }
        public string Pays { get; set; }
        public string TelephoneDomicile { get; set; }
        public string TelephoneTravail { get; set; }
        public string TelephonePortable { get; set; }
        public string Email { get; set; }
        public string Profession { get; set; }
        public string Employeur { get; set; }
        public bool EstActif { get; set; }
        public DateTime DateInscription { get; set; }
        public DateTime? DateRadiation { get; set; }
        public string MotifRadiation { get; set; }
        public int? TypeAdherentId { get; set; }
        public int? SectionId { get; set; }
        public int? FoyerId { get; set; }
        public string Observations { get; set; }
        public DateTime DateCreation { get; set; }
        public DateTime DateModification { get; set; }

        // Propriétés de navigation
        public TypeAdherent TypeAdherent { get; set; }
        public Section Section { get; set; }
        public Foyer Foyer { get; set; }
    }

    public class TypeAdherent
    {
        public int Id { get; set; }
        public string Libelle { get; set; }
        public string Description { get; set; }
        public decimal TarifCotisation { get; set; }
        public bool EstActif { get; set; }
        public DateTime DateCreation { get; set; }
    }

    public class Section
    {
        public int Id { get; set; }
        public string Libelle { get; set; }
        public string Description { get; set; }
        public string Responsable { get; set; }
        public bool EstActive { get; set; }
        public DateTime DateCreation { get; set; }
    }

    public class Foyer
    {
        public int Id { get; set; }
        public string NomFoyer { get; set; }
        public string AdressePrincipale { get; set; }
        public string CodePostal { get; set; }
        public string Ville { get; set; }
        public string TelephonePrincipal { get; set; }
        public string EmailPrincipal { get; set; }
        public DateTime DateCreation { get; set; }
    }
}
