using System;
using System.Collections.Generic;

namespace GestionAssociations.Models
{
    public class Adherent
    {
        public int Id { get; set; }
        public string NumeroAdherent { get; set; }

        // Identité de l'adhérent
        public string RaisonSociale { get; set; }
        public string Civilite { get; set; } // <PERSON><PERSON><PERSON><PERSON>, Féminin, Autre
        public string Nom { get; set; }
        public string Prenom { get; set; }
        public string RepresentantLegal { get; set; }
        public DateTime? DateNaissance { get; set; }
        public string LieuNaissance { get; set; }
        public string Nationalite { get; set; }
        public bool NonArchive { get; set; } = true;
        public DateTime? DerniereMiseAJour { get; set; }

        // Adresse 1
        public string TypeAdresse1 { get; set; }
        public string ComplementAdresse1 { get; set; }
        public string NumeroVoie1 { get; set; }
        public string NomVoie1 { get; set; }
        public string CodePostal1 { get; set; }
        public string Ville1 { get; set; }
        public string Pays1 { get; set; } = "France";
        public string Email1 { get; set; }
        public string TelDomicile1 { get; set; }
        public string TelMobile1 { get; set; }
        public string TelBureau1 { get; set; }
        public string Fax1 { get; set; }
        public bool AdresseErronee1 { get; set; }

        // Adresse 2
        public string TypeAdresse2 { get; set; }
        public string ComplementAdresse2 { get; set; }
        public string NumeroVoie2 { get; set; }
        public string NomVoie2 { get; set; }
        public string CodePostal2 { get; set; }
        public string Ville2 { get; set; }
        public string Pays2 { get; set; } = "France";
        public string Email2 { get; set; }
        public string TelDomicile2 { get; set; }
        public string TelMobile2 { get; set; }
        public string TelBureau2 { get; set; }
        public string Fax2 { get; set; }
        public bool AdresseErronee2 { get; set; }

        // Préférences courrier et email
        public bool EnvoyerCourrierAdresse1 { get; set; } = true;
        public bool EnvoyerCourrierAdresse2 { get; set; }
        public bool EnvoyerCourrierLesDeux { get; set; }
        public bool EnvoyerEmailAdresse1 { get; set; } = true;
        public bool EnvoyerEmailAdresse2 { get; set; }
        public bool EnvoyerEmailLesDeux { get; set; }

        // Communication
        public bool EnvoyerCourriers { get; set; } = true;
        public bool ImprimerEtiquettes { get; set; } = true;
        public bool EnvoyerEmails { get; set; } = true;

        // Adhésion
        public DateTime DateAdhesion { get; set; }
        public string TypeAdherent2024_2025 { get; set; }
        public string NumeroLicence1 { get; set; }
        public string NumeroLicence2 { get; set; }
        public DateTime? DateDemission { get; set; }
        public DateTime? DateEviction { get; set; }
        public string PoleSelection { get; set; }

        // Gestion des cotisations
        public bool ChefDeFamille { get; set; }
        public int NbPaiementsParDefaut { get; set; } = 1;
        public string MoyenPaiementPrefere { get; set; } = "Carte bancaire";
        public string BanqueAdherent { get; set; }

        // Droit d'entrée
        public decimal MontantARegler { get; set; }
        public decimal MontantPaye { get; set; }
        public DateTime? DatePaiement { get; set; }

        // Suivi médical
        public bool GererCertificatMedical { get; set; } = true;
        public int? MedecinTraitantId { get; set; }

        // Propriétés héritées
        public string Profession { get; set; }
        public string Employeur { get; set; }
        public bool EstActif { get; set; } = true;
        public DateTime DateInscription { get; set; }
        public DateTime? DateRadiation { get; set; }
        public string MotifRadiation { get; set; }
        public int? TypeAdherentId { get; set; }
        public int? SectionId { get; set; }
        public int? FoyerId { get; set; }
        public string Observations { get; set; }
        public DateTime DateCreation { get; set; }
        public DateTime DateModification { get; set; }

        // Propriétés de navigation
        public TypeAdherent TypeAdherent { get; set; }
        public Section Section { get; set; }
        public Foyer Foyer { get; set; }
        public Medecin MedecinTraitant { get; set; }
        public List<PersonneAPrevenir> PersonnesAPrevenir { get; set; } = new List<PersonneAPrevenir>();
        public List<AdherentSection> Sections { get; set; } = new List<AdherentSection>();
        public List<CotisationAdherent> Cotisations { get; set; } = new List<CotisationAdherent>();
        public List<FichierAdherent> Fichiers { get; set; } = new List<FichierAdherent>();
        public List<EmailAdherent> Emails { get; set; } = new List<EmailAdherent>();
    }

    public class TypeAdherent
    {
        public int Id { get; set; }
        public string Libelle { get; set; }
        public string Description { get; set; }
        public decimal TarifCotisation { get; set; }
        public bool EstActif { get; set; }
        public DateTime DateCreation { get; set; }
    }

    public class Section
    {
        public int Id { get; set; }
        public string Libelle { get; set; }
        public string Description { get; set; }
        public string Responsable { get; set; }
        public bool EstActive { get; set; }
        public DateTime DateCreation { get; set; }
    }

    public class Foyer
    {
        public int Id { get; set; }
        public string NomFoyer { get; set; }
        public string AdressePrincipale { get; set; }
        public string CodePostal { get; set; }
        public string Ville { get; set; }
        public string TelephonePrincipal { get; set; }
        public string EmailPrincipal { get; set; }
        public DateTime DateCreation { get; set; }
    }

    public class PersonneAPrevenir
    {
        public int Id { get; set; }
        public int AdherentId { get; set; }
        public string NomPrenom { get; set; }
        public string TelDomicile { get; set; }
        public string TelBureau { get; set; }
        public string TelPortable { get; set; }
        public string Email { get; set; }
        public DateTime DateCreation { get; set; }

        public Adherent Adherent { get; set; }
    }

    public class Medecin
    {
        public int Id { get; set; }
        public string NomPrenom { get; set; }
        public string ComplementAdresse { get; set; }
        public string NumeroVoie { get; set; }
        public string NomVoie { get; set; }
        public string CodePostal { get; set; }
        public string Ville { get; set; }
        public string Telephone { get; set; }
        public string Mobile { get; set; }
        public string NumeroOrdre { get; set; }
        public string Fax { get; set; }
        public bool EstActif { get; set; } = true;
        public DateTime DateCreation { get; set; }
    }

    public class AdherentSection
    {
        public int Id { get; set; }
        public int AdherentId { get; set; }
        public int SectionId { get; set; }
        public string LibelleSection { get; set; }
        public string Periode { get; set; }
        public DateTime DateCreation { get; set; }

        public Adherent Adherent { get; set; }
        public Section Section { get; set; }
    }

    public class CotisationAdherent
    {
        public int Id { get; set; }
        public int AdherentId { get; set; }
        public string Periode { get; set; }
        public string Libelle { get; set; }
        public decimal Montant { get; set; }
        public DateTime? DatePaiement { get; set; }
        public string ModePaiement { get; set; }
        public DateTime DateCreation { get; set; }

        public Adherent Adherent { get; set; }
    }

    public class FichierAdherent
    {
        public int Id { get; set; }
        public int AdherentId { get; set; }
        public string NomDocument { get; set; }
        public string CheminFichier { get; set; }
        public DateTime DateAjout { get; set; }
        public long TailleFichier { get; set; }
        public string TypeMime { get; set; }

        public Adherent Adherent { get; set; }
    }

    public class EmailAdherent
    {
        public int Id { get; set; }
        public int AdherentId { get; set; }
        public DateTime DateEnvoi { get; set; }
        public string Sujet { get; set; }
        public string Corps { get; set; }
        public bool PieceJointe { get; set; }
        public string StatutEnvoi { get; set; }

        public Adherent Adherent { get; set; }
    }
}
