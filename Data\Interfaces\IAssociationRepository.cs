using System.Threading.Tasks;
using GestionAssociations.Models;

namespace GestionAssociations.Data.Interfaces
{
    /// <summary>
    /// Interface pour le repository des associations
    /// </summary>
    public interface IAssociationRepository : IRepository<Association>
    {
        /// <summary>
        /// Obtient l'association principale
        /// </summary>
        /// <returns>Association principale</returns>
        Task<Association> GetMainAssociationAsync();

        /// <summary>
        /// Met à jour le logo de l'association
        /// </summary>
        /// <param name="associationId">ID de l'association</param>
        /// <param name="logo">Données du logo</param>
        /// <returns>True si la mise à jour a réussi</returns>
        Task<bool> UpdateLogoAsync(int associationId, byte[] logo);

        /// <summary>
        /// Met à jour les informations du président
        /// </summary>
        /// <param name="associationId">ID de l'association</param>
        /// <param name="president">Informations du président</param>
        /// <returns>True si la mise à jour a réussi</returns>
        Task<bool> UpdatePresidentAsync(int associationId, string president);

        /// <summary>
        /// Met à jour les informations du trésorier
        /// </summary>
        /// <param name="associationId">ID de l'association</param>
        /// <param name="tresorier">Informations du trésorier</param>
        /// <returns>True si la mise à jour a réussi</returns>
        Task<bool> UpdateTresorierAsync(int associationId, string tresorier);

        /// <summary>
        /// Met à jour les informations du secrétaire
        /// </summary>
        /// <param name="associationId">ID de l'association</param>
        /// <param name="secretaire">Informations du secrétaire</param>
        /// <returns>True si la mise à jour a réussi</returns>
        Task<bool> UpdateSecretaireAsync(int associationId, string secretaire);
    }
}
