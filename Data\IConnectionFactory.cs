using System.Data;

namespace GestionAssociations.Data
{
    /// <summary>
    /// Interface pour la factory de connexions à la base de données
    /// </summary>
    public interface IConnectionFactory
    {
        /// <summary>
        /// Crée une nouvelle connexion à la base de données
        /// </summary>
        /// <returns>Connexion à la base de données</returns>
        IDbConnection CreateConnection();
        
        /// <summary>
        /// Obtient la chaîne de connexion
        /// </summary>
        string ConnectionString { get; }
    }
}
