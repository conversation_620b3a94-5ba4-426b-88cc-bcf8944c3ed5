# Gestion des Associations

Application Windows Forms développée en C# avec .NET Framework 4.8 et Dapper pour la gestion complète des associations.

## Fonctionnalités

### Modules Principaux

1. **ASSOCIATION**
   - Tableau de bord avec statistiques
   - Stockage sur serveur
   - Description de l'association (✅ Implémenté)
   - Gestion des pôles et secteurs
   - Membres du bureau (✅ Implémenté)
     - Assemblées générales
     - Commissaires aux comptes
     - Membres du bureau
     - Membres du conseil d'administration
   - Signatures et droits utilisateurs (✅ Implémenté)
     - Gestion des utilisateurs par pôles
     - Gestionnaires de compte client

2. **ADHÉRENTS**
   - Gestion des adhérents
   - Types d'adhérents et sections
   - Cotisations et pièces justificatives
   - Import/Export de données
   - Statistiques

3. **COMPTABILITÉ**
   - Comptes bancaires et chéquiers
   - Plan comptable et tiers
   - Budgets et événements comptables
   - Bilans et comptes de résultat

4. **ORGANISER**
   - Activités et manifestations
   - Inscriptions et participants
   - Plans types

5. **SECRÉTARIAT**
   - Courriers et publipostage
   - Emails et SMS
   - Contacts et étiquettes

6. **MATÉRIELS**
   - Inventaire et gestion du matériel
   - Entrées et sorties
   - Statistiques matériels

## Technologies Utilisées

- **.NET Framework 4.8**
- **Windows Forms** pour l'interface utilisateur
- **Dapper** comme ORM pour l'accès aux données
- **SQL Server** pour la base de données
- **C#** comme langage de programmation

## Structure du Projet

```
GestionAssociations/
├── Forms/                          # Formulaires Windows Forms
│   ├── MainForm.cs                 # Formulaire principal avec menu
│   └── Association/
│       ├── DescriptionAssociationForm.cs  # Gestion des informations association
│       ├── MembresBureauForm.cs           # Gestion des membres du bureau
│       ├── NouvelleAssembleeForm.cs       # Création d'assemblée générale
│       ├── NouveauCommissaireForm.cs      # Création de commissaire aux comptes
│       ├── NouveauMembreBureauForm.cs     # Création de membre du bureau/CA
│       └── GestionUtilisateursForm.cs     # Gestion des utilisateurs
├── Models/                         # Modèles de données
│   ├── Association.cs
│   ├── Adherent.cs
│   ├── Cotisation.cs
│   ├── Pole.cs
│   └── Bureau.cs
├── Data/                          # Couche d'accès aux données
│   ├── DatabaseConnection.cs
│   └── Repositories/
│       ├── IRepository.cs
│       ├── BaseRepository.cs
│       └── AssociationRepository.cs
├── Services/                      # Services métier
│   └── AssociationService.cs
├── Database/                      # Scripts SQL
│   └── CreateDatabase.sql
└── Properties/                    # Propriétés du projet
```

## Installation et Configuration

### Prérequis

1. **Visual Studio 2019 ou plus récent**
2. **SQL Server** (LocalDB, Express, ou version complète)
3. **.NET Framework 4.8**

### Configuration de la Base de Données

1. **Créer la base de données :**
   ```sql
   -- Exécuter le script Database/CreateDatabase.sql dans SQL Server Management Studio
   ```

2. **Configurer la chaîne de connexion :**
   ```xml
   <!-- Dans App.config -->
   <connectionStrings>
     <add name="DefaultConnection" 
          connectionString="Data Source=localhost;Initial Catalog=GestionAssociations;Integrated Security=True" 
          providerName="System.Data.SqlClient" />
   </connectionStrings>
   ```

### Installation des Packages NuGet

Les packages suivants sont requis (déjà configurés dans packages.config) :

- **Dapper** (2.1.35) - ORM léger pour .NET
- **System.Data.SqlClient** (4.8.6) - Connecteur SQL Server
- **Newtonsoft.Json** (13.0.3) - Sérialisation JSON

## Utilisation

### Démarrage de l'Application

1. Ouvrir le projet dans Visual Studio
2. Restaurer les packages NuGet
3. Compiler et exécuter l'application
4. Le formulaire principal s'ouvre avec le menu des 6 modules

### Navigation

- **Menu principal** : 6 modules avec sous-menus déroulants
- **Formulaires modaux** : Les formulaires s'ouvrent en mode modal
- **Validation** : Boutons de validation pour sauvegarder les modifications

### Fonctionnalités Implémentées

#### Description Association (✅ Complet)

- **Onglet Coordonnées** : Informations de base de l'association
- **Onglet But** : Description des objectifs et activités
- **Onglets Administration, Président, Trésorier, Secrétaire** : Informations des responsables
- **Onglet Logo** : Gestion du logo de l'association

#### Membres du Bureau (✅ Complet)

- **Assemblées générales** : Gestion des dates d'assemblées avec descriptions
- **Commissaires aux comptes** : Gestion des commissaires avec fonctions et mandats
- **Membres du bureau** : Liste des membres avec fonctions (Président, Vice-Président, etc.)
- **Membres du conseil d'administration** : Gestion du conseil d'administration
- **Formulaires de saisie** : Création et modification des entités
- **Fonctionnalités** : Dupliquer, Nouveau, Modifier, Supprimer, Imprimer

#### Gestion des Utilisateurs (✅ Complet)

- **Liste des utilisateurs** : Gestion par pôles avec identifiants uniques
- **Gestionnaire de compte client** : Principal et secondaire
- **Interface moderne** : DataGridView avec données de démonstration
- **Actions** : Nouveau, Modifier, Supprimer

## Développement

### Architecture

L'application suit une architecture en couches :

1. **Présentation** (Forms) - Interface utilisateur Windows Forms
2. **Services** - Logique métier et orchestration
3. **Repositories** - Accès aux données avec Dapper
4. **Models** - Entités et modèles de données

### Patterns Utilisés

- **Repository Pattern** pour l'accès aux données
- **Service Layer** pour la logique métier
- **Dependency Injection** (manuel) pour le découplage

### Ajout de Nouveaux Formulaires

1. Créer le formulaire dans le dossier approprié sous `Forms/`
2. Implémenter l'interface utilisateur
3. Ajouter la logique métier dans les services
4. Mettre à jour la méthode `ShowForm()` dans `MainForm.cs`

## Roadmap

### Prochaines Fonctionnalités

- [ ] Tableau de bord avec graphiques
- [ ] Gestion des adhérents
- [ ] Module comptabilité
- [ ] Système d'authentification
- [ ] Rapports et exports
- [ ] Sauvegarde automatique

### Améliorations Techniques

- [ ] Migration vers .NET 6/8
- [ ] Interface plus moderne (WPF ou Avalonia)
- [ ] Tests unitaires
- [ ] Logging structuré
- [ ] Configuration avancée

## Support

Pour toute question ou problème :

1. Vérifier la configuration de la base de données
2. S'assurer que tous les packages NuGet sont installés
3. Vérifier les logs d'erreur dans l'application

## Licence

Ce projet est développé pour la gestion des associations et peut être adapté selon les besoins spécifiques.
