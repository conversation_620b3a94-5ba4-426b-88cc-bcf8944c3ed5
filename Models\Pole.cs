using System;
using System.Collections.Generic;

namespace GestionAssociations.Models
{
    public class Pole
    {
        public int Id { get; set; }
        public string Libelle { get; set; }
        public string Description { get; set; }
        public bool EstActif { get; set; }
        public DateTime DateCreation { get; set; }
        public DateTime DateModification { get; set; }

        // Propriétés de navigation
        public List<Secteur> Secteurs { get; set; } = new List<Secteur>();
        public List<UtilisateurPole> Utilisateurs { get; set; } = new List<UtilisateurPole>();
    }

    public class Secteur
    {
        public int Id { get; set; }
        public int PoleId { get; set; }
        public string Libelle { get; set; }
        public string Description { get; set; }
        public bool EstActif { get; set; }
        public DateTime DateCreation { get; set; }

        // Propriété de navigation
        public Pole Pole { get; set; }
    }

    public class Utilisateur
    {
        public int Id { get; set; }
        public string Login { get; set; }
        public string MotDePasse { get; set; }
        public string Nom { get; set; }
        public string Prenom { get; set; }
        public string Email { get; set; }
        public bool EstActif { get; set; }
        public bool EstAdministrateur { get; set; }
        public DateTime DateCreation { get; set; }
        public DateTime? DerniereConnexion { get; set; }

        // Propriétés de navigation
        public List<UtilisateurPole> Poles { get; set; } = new List<UtilisateurPole>();
        public List<DroitUtilisateur> Droits { get; set; } = new List<DroitUtilisateur>();
    }

    public class UtilisateurPole
    {
        public int Id { get; set; }
        public int UtilisateurId { get; set; }
        public int PoleId { get; set; }
        public bool EstResponsable { get; set; }
        public DateTime DateAffectation { get; set; }

        // Propriétés de navigation
        public Utilisateur Utilisateur { get; set; }
        public Pole Pole { get; set; }
    }

    public class DroitUtilisateur
    {
        public int Id { get; set; }
        public int UtilisateurId { get; set; }
        public string Module { get; set; }
        public string Action { get; set; }
        public bool EstAutorise { get; set; }
        public DateTime DateCreation { get; set; }

        // Propriété de navigation
        public Utilisateur Utilisateur { get; set; }
    }
}
