using System;

namespace GestionAssociations.Models
{
    public class AssembleeGenerale
    {
        public int Id { get; set; }
        public DateTime DateAssemblee { get; set; }
        public string Description { get; set; }
        public string Observations { get; set; }
        public DateTime DateCreation { get; set; }
        public DateTime DateModification { get; set; }
    }

    public class CommissaireAuxComptes
    {
        public int Id { get; set; }
        public int AssembleeId { get; set; }
        public string NomPrenom { get; set; }
        public string Fonction { get; set; }
        public string Cabinet { get; set; }
        public DateTime DateDebut { get; set; }
        public DateTime DateFin { get; set; }
        public bool EstActif { get; set; }
        public DateTime DateCreation { get; set; }

        // Propriété de navigation
        public AssembleeGenerale Assemblee { get; set; }
    }

    public class MembreBureau
    {
        public int Id { get; set; }
        public int AssembleeId { get; set; }
        public int AdherentId { get; set; }
        public string NomPrenom { get; set; }
        public string Fonction { get; set; }
        public string Email { get; set; }
        public string Telephone { get; set; }
        public string LieuNaissance { get; set; }
        public string Nationalite { get; set; }
        public string Profession { get; set; }
        public DateTime DateDebut { get; set; }
        public DateTime DateFin { get; set; }
        public bool EstMembreBureau { get; set; }
        public bool EstMembreCA { get; set; }
        public bool EstActif { get; set; }
        public DateTime DateCreation { get; set; }

        // Propriétés de navigation
        public AssembleeGenerale Assemblee { get; set; }
        public Adherent Adherent { get; set; }
    }

    public class MembreConseilAdministration
    {
        public int Id { get; set; }
        public int AssembleeId { get; set; }
        public int AdherentId { get; set; }
        public string NomPrenom { get; set; }
        public string Fonction { get; set; }
        public string Email { get; set; }
        public string Telephone { get; set; }
        public DateTime DateDebut { get; set; }
        public DateTime DateFin { get; set; }
        public bool EstActif { get; set; }
        public DateTime DateCreation { get; set; }

        // Propriétés de navigation
        public AssembleeGenerale Assemblee { get; set; }
        public Adherent Adherent { get; set; }
    }

    public class FonctionBureau
    {
        public int Id { get; set; }
        public string Libelle { get; set; }
        public string Description { get; set; }
        public int Ordre { get; set; }
        public bool EstActif { get; set; }
        public DateTime DateCreation { get; set; }
    }
}
