using System;
using FluentValidation;
using GestionAssociations.Models;

namespace GestionAssociations.Core.Validators
{
    /// <summary>
    /// Validateur pour les adhérents
    /// </summary>
    public class AdherentValidator : AbstractValidator<Adherent>
    {
        public AdherentValidator()
        {
            // Validation du nom (obligatoire)
            RuleFor(x => x.Nom)
                .NotEmpty().WithMessage("Le nom est obligatoire")
                .MaximumLength(100).WithMessage("Le nom ne peut dépasser 100 caractères")
                .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$").WithMessage("Le nom ne peut contenir que des lettres, espaces, tirets et apostrophes");

            // Validation du prénom (obligatoire)
            RuleFor(x => x.Prenom)
                .NotEmpty().WithMessage("Le prénom est obligatoire")
                .MaximumLength(100).WithMessage("Le prénom ne peut dépasser 100 caractères")
                .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$").WithMessage("Le prénom ne peut contenir que des lettres, espaces, tirets et apostrophes");

            // Validation de la civilité
            RuleFor(x => x.Civilite)
                .Must(BeValidCivilite).WithMessage("La civilité doit être 'Masculin', 'Féminin' ou 'Autre'")
                .When(x => !string.IsNullOrEmpty(x.Civilite));

            // Validation de la date de naissance
            RuleFor(x => x.DateNaissance)
                .LessThan(DateTime.Today).WithMessage("La date de naissance doit être antérieure à aujourd'hui")
                .GreaterThan(DateTime.Today.AddYears(-120)).WithMessage("La date de naissance ne peut pas être antérieure à 120 ans")
                .When(x => x.DateNaissance.HasValue);

            // Validation de l'email principal
            RuleFor(x => x.Email1)
                .EmailAddress().WithMessage("Format d'email invalide pour l'adresse principale")
                .MaximumLength(255).WithMessage("L'email principal ne peut dépasser 255 caractères")
                .When(x => !string.IsNullOrEmpty(x.Email1));

            // Validation de l'email secondaire
            RuleFor(x => x.Email2)
                .EmailAddress().WithMessage("Format d'email invalide pour l'adresse secondaire")
                .MaximumLength(255).WithMessage("L'email secondaire ne peut dépasser 255 caractères")
                .When(x => !string.IsNullOrEmpty(x.Email2));

            // Validation du téléphone domicile 1
            RuleFor(x => x.TelDomicile1)
                .Matches(@"^[\d\s\-\.\(\)\+]+$").WithMessage("Format de téléphone invalide")
                .MaximumLength(20).WithMessage("Le numéro de téléphone ne peut dépasser 20 caractères")
                .When(x => !string.IsNullOrEmpty(x.TelDomicile1));

            // Validation du téléphone mobile 1
            RuleFor(x => x.TelMobile1)
                .Matches(@"^[\d\s\-\.\(\)\+]+$").WithMessage("Format de téléphone mobile invalide")
                .MaximumLength(20).WithMessage("Le numéro de téléphone mobile ne peut dépasser 20 caractères")
                .When(x => !string.IsNullOrEmpty(x.TelMobile1));

            // Validation du code postal 1
            RuleFor(x => x.CodePostal1)
                .Matches(@"^\d{5}$").WithMessage("Le code postal doit contenir exactement 5 chiffres")
                .When(x => !string.IsNullOrEmpty(x.CodePostal1));

            // Validation du code postal 2
            RuleFor(x => x.CodePostal2)
                .Matches(@"^\d{5}$").WithMessage("Le code postal doit contenir exactement 5 chiffres")
                .When(x => !string.IsNullOrEmpty(x.CodePostal2));

            // Validation de la ville 1
            RuleFor(x => x.Ville1)
                .MaximumLength(100).WithMessage("Le nom de ville ne peut dépasser 100 caractères")
                .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$").WithMessage("Le nom de ville ne peut contenir que des lettres, espaces, tirets et apostrophes")
                .When(x => !string.IsNullOrEmpty(x.Ville1));

            // Validation de la ville 2
            RuleFor(x => x.Ville2)
                .MaximumLength(100).WithMessage("Le nom de ville ne peut dépasser 100 caractères")
                .Matches(@"^[a-zA-ZÀ-ÿ\s\-']+$").WithMessage("Le nom de ville ne peut contenir que des lettres, espaces, tirets et apostrophes")
                .When(x => !string.IsNullOrEmpty(x.Ville2));

            // Validation de la raison sociale
            RuleFor(x => x.RaisonSociale)
                .MaximumLength(255).WithMessage("La raison sociale ne peut dépasser 255 caractères")
                .When(x => !string.IsNullOrEmpty(x.RaisonSociale));

            // Validation du représentant légal
            RuleFor(x => x.RepresentantLegal)
                .MaximumLength(255).WithMessage("Le nom du représentant légal ne peut dépasser 255 caractères")
                .When(x => !string.IsNullOrEmpty(x.RepresentantLegal));

            // Validation de la date d'adhésion
            RuleFor(x => x.DateAdhesion)
                .LessThanOrEqualTo(DateTime.Today).WithMessage("La date d'adhésion ne peut pas être dans le futur")
                .GreaterThan(DateTime.Today.AddYears(-50)).WithMessage("La date d'adhésion ne peut pas être antérieure à 50 ans");

            // Validation de la date de démission
            RuleFor(x => x.DateDemission)
                .GreaterThan(x => x.DateAdhesion).WithMessage("La date de démission doit être postérieure à la date d'adhésion")
                .LessThanOrEqualTo(DateTime.Today).WithMessage("La date de démission ne peut pas être dans le futur")
                .When(x => x.DateDemission.HasValue);

            // Validation de la date d'éviction
            RuleFor(x => x.DateEviction)
                .GreaterThan(x => x.DateAdhesion).WithMessage("La date d'éviction doit être postérieure à la date d'adhésion")
                .LessThanOrEqualTo(DateTime.Today).WithMessage("La date d'éviction ne peut pas être dans le futur")
                .When(x => x.DateEviction.HasValue);

            // Validation des montants
            RuleFor(x => x.MontantARegler)
                .GreaterThanOrEqualTo(0).WithMessage("Le montant à régler ne peut pas être négatif");

            RuleFor(x => x.MontantPaye)
                .GreaterThanOrEqualTo(0).WithMessage("Le montant payé ne peut pas être négatif")
                .LessThanOrEqualTo(x => x.MontantARegler).WithMessage("Le montant payé ne peut pas être supérieur au montant à régler")
                .When(x => x.MontantARegler > 0);

            // Validation de la date de paiement
            RuleFor(x => x.DatePaiement)
                .LessThanOrEqualTo(DateTime.Today).WithMessage("La date de paiement ne peut pas être dans le futur")
                .When(x => x.DatePaiement.HasValue);

            // Validation du nombre de paiements par défaut
            RuleFor(x => x.NbPaiementsParDefaut)
                .GreaterThan(0).WithMessage("Le nombre de paiements par défaut doit être supérieur à 0")
                .LessThanOrEqualTo(12).WithMessage("Le nombre de paiements par défaut ne peut pas dépasser 12");

            // Validation des observations
            RuleFor(x => x.Observations)
                .MaximumLength(1000).WithMessage("Les observations ne peuvent dépasser 1000 caractères")
                .When(x => !string.IsNullOrEmpty(x.Observations));

            // Validation de la profession
            RuleFor(x => x.Profession)
                .MaximumLength(100).WithMessage("La profession ne peut dépasser 100 caractères")
                .When(x => !string.IsNullOrEmpty(x.Profession));

            // Validation de l'employeur
            RuleFor(x => x.Employeur)
                .MaximumLength(255).WithMessage("L'employeur ne peut dépasser 255 caractères")
                .When(x => !string.IsNullOrEmpty(x.Employeur));

            // Validation de la banque
            RuleFor(x => x.BanqueAdherent)
                .MaximumLength(100).WithMessage("Le nom de la banque ne peut dépasser 100 caractères")
                .When(x => !string.IsNullOrEmpty(x.BanqueAdherent));

            // Validation du moyen de paiement préféré
            RuleFor(x => x.MoyenPaiementPrefere)
                .Must(BeValidPaymentMethod).WithMessage("Moyen de paiement invalide")
                .When(x => !string.IsNullOrEmpty(x.MoyenPaiementPrefere));

            // Validation des numéros de licence
            RuleFor(x => x.NumeroLicence1)
                .MaximumLength(50).WithMessage("Le numéro de licence 1 ne peut dépasser 50 caractères")
                .When(x => !string.IsNullOrEmpty(x.NumeroLicence1));

            RuleFor(x => x.NumeroLicence2)
                .MaximumLength(50).WithMessage("Le numéro de licence 2 ne peut dépasser 50 caractères")
                .When(x => !string.IsNullOrEmpty(x.NumeroLicence2));

            // Validation de cohérence : au moins une adresse doit être renseignée
            RuleFor(x => x)
                .Must(HaveAtLeastOneAddress).WithMessage("Au moins une adresse doit être renseignée");

            // Validation de cohérence : si chef de famille, doit avoir une adresse complète
            RuleFor(x => x)
                .Must(HaveCompleteAddressIfChefDeFamille).WithMessage("Un chef de famille doit avoir une adresse complète")
                .When(x => x.ChefDeFamille);
        }

        private bool BeValidCivilite(string civilite)
        {
            var validCivilites = new[] { "Masculin", "Féminin", "Autre" };
            return Array.Exists(validCivilites, c => c.Equals(civilite, StringComparison.OrdinalIgnoreCase));
        }

        private bool BeValidPaymentMethod(string paymentMethod)
        {
            var validMethods = new[] { "Carte bancaire", "Chèque", "Espèces", "Virement", "Prélèvement" };
            return Array.Exists(validMethods, m => m.Equals(paymentMethod, StringComparison.OrdinalIgnoreCase));
        }

        private bool HaveAtLeastOneAddress(Adherent adherent)
        {
            return !string.IsNullOrEmpty(adherent.NomVoie1) || !string.IsNullOrEmpty(adherent.NomVoie2);
        }

        private bool HaveCompleteAddressIfChefDeFamille(Adherent adherent)
        {
            if (!adherent.ChefDeFamille) return true;

            return (!string.IsNullOrEmpty(adherent.NomVoie1) && !string.IsNullOrEmpty(adherent.Ville1) && !string.IsNullOrEmpty(adherent.CodePostal1)) ||
                   (!string.IsNullOrEmpty(adherent.NomVoie2) && !string.IsNullOrEmpty(adherent.Ville2) && !string.IsNullOrEmpty(adherent.CodePostal2));
        }
    }
}
