using System;
using System.Drawing;
using System.Windows.Forms;
using GestionAssociations.Models;

namespace GestionAssociations.Forms.Association
{
    public partial class MembresBureauForm : Form
    {
        private TabControl tabControl;
        private TabPage tabAssemblees;
        private TabPage tabCommissaires;
        private TabPage tabMembresBureau;
        private TabPage tabMembresCA;

        // Contrôles pour Assemblées générales
        private DataGridView dgvAssemblees;
        private Button btnNouvelleAssemblee;
        private Button btnModifierAssemblee;
        private Button btnSupprimerAssemblee;
        private Button btnDupliquerAssemblee;
        private Button btnImprimerAssemblee;

        // Contrôles pour Commissaires aux comptes
        private DataGridView dgvCommissaires;
        private Button btnNouveauCommissaire;
        private Button btnModifierCommissaire;
        private Button btnSupprimerCommissaire;

        // Contrôles pour Membres du bureau
        private DataGridView dgvMembresBureau;
        private Button btnNouveauMembreBureau;
        private Button btnModifierMembreBureau;
        private Button btnSupprimerMembreBureau;
        private Button btnDupliquerMembreBureau;

        // Contrôles pour Membres du CA
        private DataGridView dgvMembresCA;
        private Button btnNouveauMembreCA;
        private Button btnModifierMembreCA;
        private Button btnSupprimerMembreCA;

        public MembresBureauForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            LoadData();
        }

        private void InitializeCustomComponents()
        {
            this.Text = "Membres du bureau";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;

            // Créer le TabControl
            tabControl = new TabControl();
            tabControl.Dock = DockStyle.Fill;
            tabControl.Font = new Font("Segoe UI", 9F);

            // Onglet Assemblées générales
            tabAssemblees = new TabPage("Assemblée générale");
            CreateAssembleesTab();

            // Onglet Commissaires aux comptes
            tabCommissaires = new TabPage("Commissaires aux comptes");
            CreateCommissairesTab();

            // Onglet Membres du bureau
            tabMembresBureau = new TabPage("Membres du bureau");
            CreateMembresBureauTab();

            // Onglet Membres du CA
            tabMembresCA = new TabPage("Membres du conseil d'administration");
            CreateMembresCaTab();

            tabControl.TabPages.AddRange(new TabPage[] {
                tabAssemblees, tabCommissaires, tabMembresBureau, tabMembresCA
            });

            this.Controls.Add(tabControl);
        }

        private void CreateAssembleesTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // DataGridView pour les assemblées
            dgvAssemblees = new DataGridView
            {
                Location = new Point(10, 10),
                Size = new Size(1150, 400),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Colonnes pour les assemblées
            dgvAssemblees.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "DateAssemblee", HeaderText = "Date assemblée", DataPropertyName = "DateAssemblee" },
                new DataGridViewTextBoxColumn { Name = "Description", HeaderText = "Description", DataPropertyName = "Description" },
                new DataGridViewTextBoxColumn { Name = "Observations", HeaderText = "Observations", DataPropertyName = "Observations" }
            });

            // Boutons pour les assemblées
            var buttonPanel = new Panel
            {
                Location = new Point(10, 420),
                Size = new Size(1150, 50),
                BackColor = Color.FromArgb(240, 240, 240)
            };

            btnDupliquerAssemblee = CreateButton("Dupliquer", new Point(10, 10), Color.FromArgb(70, 70, 100));
            btnNouvelleAssemblee = CreateButton("Nouveau", new Point(120, 10), Color.FromArgb(70, 70, 100));
            btnModifierAssemblee = CreateButton("Modifier", new Point(230, 10), Color.FromArgb(70, 70, 100));
            btnSupprimerAssemblee = CreateButton("Supprimer", new Point(340, 10), Color.FromArgb(200, 50, 50));
            btnImprimerAssemblee = CreateButton("Imprimer", new Point(450, 10), Color.FromArgb(70, 70, 100));

            // Événements
            btnNouvelleAssemblee.Click += BtnNouvelleAssemblee_Click;
            btnModifierAssemblee.Click += BtnModifierAssemblee_Click;
            btnSupprimerAssemblee.Click += BtnSupprimerAssemblee_Click;
            btnDupliquerAssemblee.Click += BtnDupliquerAssemblee_Click;
            btnImprimerAssemblee.Click += BtnImprimerAssemblee_Click;

            buttonPanel.Controls.AddRange(new Control[] {
                btnDupliquerAssemblee, btnNouvelleAssemblee, btnModifierAssemblee, 
                btnSupprimerAssemblee, btnImprimerAssemblee
            });

            panel.Controls.AddRange(new Control[] { dgvAssemblees, buttonPanel });
            tabAssemblees.Controls.Add(panel);
        }

        private void CreateCommissairesTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // DataGridView pour les commissaires
            dgvCommissaires = new DataGridView
            {
                Location = new Point(10, 10),
                Size = new Size(1150, 400),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Colonnes pour les commissaires
            dgvCommissaires.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "NomPrenom", HeaderText = "Nom et prénom", DataPropertyName = "NomPrenom" },
                new DataGridViewTextBoxColumn { Name = "Fonction", HeaderText = "Fonction", DataPropertyName = "Fonction" }
            });

            // Boutons pour les commissaires
            var buttonPanel = new Panel
            {
                Location = new Point(10, 420),
                Size = new Size(1150, 50),
                BackColor = Color.FromArgb(240, 240, 240)
            };

            btnNouveauCommissaire = CreateButton("Nouveau", new Point(10, 10), Color.FromArgb(70, 70, 100));
            btnModifierCommissaire = CreateButton("Modifier", new Point(120, 10), Color.FromArgb(70, 70, 100));
            btnSupprimerCommissaire = CreateButton("Supprimer", new Point(230, 10), Color.FromArgb(200, 50, 50));

            // Événements
            btnNouveauCommissaire.Click += BtnNouveauCommissaire_Click;
            btnModifierCommissaire.Click += BtnModifierCommissaire_Click;
            btnSupprimerCommissaire.Click += BtnSupprimerCommissaire_Click;

            buttonPanel.Controls.AddRange(new Control[] {
                btnNouveauCommissaire, btnModifierCommissaire, btnSupprimerCommissaire
            });

            panel.Controls.AddRange(new Control[] { dgvCommissaires, buttonPanel });
            tabCommissaires.Controls.Add(panel);
        }

        private void CreateMembresBureauTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // DataGridView pour les membres du bureau
            dgvMembresBureau = new DataGridView
            {
                Location = new Point(10, 10),
                Size = new Size(1150, 400),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Colonnes pour les membres du bureau
            dgvMembresBureau.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "NomPrenom", HeaderText = "Nom et prénom", DataPropertyName = "NomPrenom" },
                new DataGridViewTextBoxColumn { Name = "Fonction", HeaderText = "Fonction", DataPropertyName = "Fonction" },
                new DataGridViewTextBoxColumn { Name = "Email", HeaderText = "Email", DataPropertyName = "Email" },
                new DataGridViewTextBoxColumn { Name = "Telephone", HeaderText = "Téléphone", DataPropertyName = "Telephone" }
            });

            // Boutons pour les membres du bureau
            var buttonPanel = new Panel
            {
                Location = new Point(10, 420),
                Size = new Size(1150, 50),
                BackColor = Color.FromArgb(240, 240, 240)
            };

            btnDupliquerMembreBureau = CreateButton("Dupliquer", new Point(10, 10), Color.FromArgb(70, 70, 100));
            btnNouveauMembreBureau = CreateButton("Nouveau", new Point(120, 10), Color.FromArgb(70, 70, 100));
            btnModifierMembreBureau = CreateButton("Modifier", new Point(230, 10), Color.FromArgb(70, 70, 100));
            btnSupprimerMembreBureau = CreateButton("Supprimer", new Point(340, 10), Color.FromArgb(200, 50, 50));

            // Événements
            btnNouveauMembreBureau.Click += BtnNouveauMembreBureau_Click;
            btnModifierMembreBureau.Click += BtnModifierMembreBureau_Click;
            btnSupprimerMembreBureau.Click += BtnSupprimerMembreBureau_Click;
            btnDupliquerMembreBureau.Click += BtnDupliquerMembreBureau_Click;

            buttonPanel.Controls.AddRange(new Control[] {
                btnDupliquerMembreBureau, btnNouveauMembreBureau, 
                btnModifierMembreBureau, btnSupprimerMembreBureau
            });

            panel.Controls.AddRange(new Control[] { dgvMembresBureau, buttonPanel });
            tabMembresBureau.Controls.Add(panel);
        }

        private void CreateMembresCaTab()
        {
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // DataGridView pour les membres du CA
            dgvMembresCA = new DataGridView
            {
                Location = new Point(10, 10),
                Size = new Size(1150, 400),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Colonnes pour les membres du CA
            dgvMembresCA.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "NomPrenom", HeaderText = "Nom et prénom", DataPropertyName = "NomPrenom" },
                new DataGridViewTextBoxColumn { Name = "Fonction", HeaderText = "Fonction", DataPropertyName = "Fonction" },
                new DataGridViewTextBoxColumn { Name = "Email", HeaderText = "Email", DataPropertyName = "Email" },
                new DataGridViewTextBoxColumn { Name = "Telephone", HeaderText = "Téléphone", DataPropertyName = "Telephone" }
            });

            // Boutons pour les membres du CA
            var buttonPanel = new Panel
            {
                Location = new Point(10, 420),
                Size = new Size(1150, 50),
                BackColor = Color.FromArgb(240, 240, 240)
            };

            btnNouveauMembreCA = CreateButton("Nouveau", new Point(10, 10), Color.FromArgb(70, 70, 100));
            btnModifierMembreCA = CreateButton("Modifier", new Point(120, 10), Color.FromArgb(70, 70, 100));
            btnSupprimerMembreCA = CreateButton("Supprimer", new Point(230, 10), Color.FromArgb(200, 50, 50));

            // Événements
            btnNouveauMembreCA.Click += BtnNouveauMembreCA_Click;
            btnModifierMembreCA.Click += BtnModifierMembreCA_Click;
            btnSupprimerMembreCA.Click += BtnSupprimerMembreCA_Click;

            buttonPanel.Controls.AddRange(new Control[] {
                btnNouveauMembreCA, btnModifierMembreCA, btnSupprimerMembreCA
            });

            panel.Controls.AddRange(new Control[] { dgvMembresCA, buttonPanel });
            tabMembresCA.Controls.Add(panel);
        }

        private Button CreateButton(string text, Point location, Color backColor)
        {
            return new Button
            {
                Text = text,
                Location = location,
                Size = new Size(100, 30),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 9F)
            };
        }

        private void LoadData()
        {
            // TODO: Charger les données depuis la base de données
            LoadAssemblees();
            LoadCommissaires();
            LoadMembresBureau();
            LoadMembresCA();
        }

        private void LoadAssemblees()
        {
            // TODO: Implémenter le chargement des assemblées
        }

        private void LoadCommissaires()
        {
            // TODO: Implémenter le chargement des commissaires
        }

        private void LoadMembresBureau()
        {
            // TODO: Implémenter le chargement des membres du bureau
        }

        private void LoadMembresCA()
        {
            // TODO: Implémenter le chargement des membres du CA
        }

        // Événements pour les assemblées
        private void BtnNouvelleAssemblee_Click(object sender, EventArgs e)
        {
            var form = new NouvelleAssembleeForm();
            if (form.ShowDialog() == DialogResult.OK)
            {
                LoadAssemblees();
            }
        }

        private void BtnModifierAssemblee_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter la modification d'assemblée
            MessageBox.Show("Modifier assemblée - À implémenter");
        }

        private void BtnSupprimerAssemblee_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter la suppression d'assemblée
            MessageBox.Show("Supprimer assemblée - À implémenter");
        }

        private void BtnDupliquerAssemblee_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter la duplication d'assemblée
            MessageBox.Show("Dupliquer assemblée - À implémenter");
        }

        private void BtnImprimerAssemblee_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter l'impression d'assemblée
            MessageBox.Show("Imprimer assemblée - À implémenter");
        }

        // Événements pour les commissaires
        private void BtnNouveauCommissaire_Click(object sender, EventArgs e)
        {
            var form = new NouveauCommissaireForm();
            if (form.ShowDialog() == DialogResult.OK)
            {
                LoadCommissaires();
            }
        }

        private void BtnModifierCommissaire_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter la modification de commissaire
            MessageBox.Show("Modifier commissaire - À implémenter");
        }

        private void BtnSupprimerCommissaire_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter la suppression de commissaire
            MessageBox.Show("Supprimer commissaire - À implémenter");
        }

        // Événements pour les membres du bureau
        private void BtnNouveauMembreBureau_Click(object sender, EventArgs e)
        {
            var form = new NouveauMembreBureauForm();
            if (form.ShowDialog() == DialogResult.OK)
            {
                LoadMembresBureau();
            }
        }

        private void BtnModifierMembreBureau_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter la modification de membre du bureau
            MessageBox.Show("Modifier membre du bureau - À implémenter");
        }

        private void BtnSupprimerMembreBureau_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter la suppression de membre du bureau
            MessageBox.Show("Supprimer membre du bureau - À implémenter");
        }

        private void BtnDupliquerMembreBureau_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter la duplication de membre du bureau
            MessageBox.Show("Dupliquer membre du bureau - À implémenter");
        }

        // Événements pour les membres du CA
        private void BtnNouveauMembreCA_Click(object sender, EventArgs e)
        {
            var form = new NouveauMembreBureauForm(true); // true pour CA
            if (form.ShowDialog() == DialogResult.OK)
            {
                LoadMembresCA();
            }
        }

        private void BtnModifierMembreCA_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter la modification de membre du CA
            MessageBox.Show("Modifier membre du CA - À implémenter");
        }

        private void BtnSupprimerMembreCA_Click(object sender, EventArgs e)
        {
            // TODO: Implémenter la suppression de membre du CA
            MessageBox.Show("Supprimer membre du CA - À implémenter");
        }
    }
}
