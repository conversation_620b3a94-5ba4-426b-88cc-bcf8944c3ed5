using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using GestionAssociations.UI.Themes;

namespace GestionAssociations.UI.Controls
{
    /// <summary>
    /// Panel de filtres configurable
    /// </summary>
    public class FilterPanel : UserControl
    {
        private readonly Dictionary<string, Control> _filters = new Dictionary<string, Control>();
        private readonly Dictionary<string, Label> _labels = new Dictionary<string, Label>();
        private int _currentX = 10;
        private int _currentY = 10;
        private const int SPACING = 10;
        private const int LABEL_WIDTH = 80;
        private const int CONTROL_WIDTH = 150;

        public FilterPanel()
        {
            InitializeComponent();
            ApplyStyle();
        }

        #region Événements

        /// <summary>
        /// Événement déclenché quand un filtre change
        /// </summary>
        public event EventHandler<FilterChangedEventArgs> FilterChanged;

        /// <summary>
        /// Événement déclenché quand les filtres sont réinitialisés
        /// </summary>
        public event EventHandler FiltersReset;

        #endregion

        private void InitializeComponent()
        {
            Size = new Size(800, 60);
            BackColor = ThemeManager.Colors.BackgroundLight;
            BorderStyle = BorderStyle.FixedSingle;
            AutoScroll = true;
        }

        private void ApplyStyle()
        {
            BackColor = ThemeManager.Colors.BackgroundLight;
            Font = ThemeManager.Fonts.Default;
        }

        /// <summary>
        /// Ajoute un filtre ComboBox
        /// </summary>
        /// <param name="name">Nom du filtre</param>
        /// <param name="label">Libellé</param>
        /// <param name="items">Éléments de la liste</param>
        /// <param name="defaultValue">Valeur par défaut</param>
        public void AddComboBoxFilter(string name, string label, string[] items, string defaultValue = null)
        {
            var labelControl = CreateLabel(label);
            var comboBox = new ComboBox
            {
                Location = new Point(_currentX + LABEL_WIDTH + SPACING, _currentY),
                Size = new Size(CONTROL_WIDTH, ThemeManager.Spacing.InputHeight),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = ThemeManager.Fonts.Default
            };

            comboBox.Items.AddRange(items);
            if (!string.IsNullOrEmpty(defaultValue) && comboBox.Items.Contains(defaultValue))
            {
                comboBox.SelectedItem = defaultValue;
            }
            else if (comboBox.Items.Count > 0)
            {
                comboBox.SelectedIndex = 0;
            }

            comboBox.SelectedIndexChanged += (s, e) => OnFilterChanged(name, comboBox.SelectedItem?.ToString());

            AddFilterControl(name, labelControl, comboBox);
        }

        /// <summary>
        /// Ajoute un filtre TextBox
        /// </summary>
        /// <param name="name">Nom du filtre</param>
        /// <param name="label">Libellé</param>
        /// <param name="placeholder">Texte de placeholder</param>
        public void AddTextBoxFilter(string name, string label, string placeholder = null)
        {
            var labelControl = CreateLabel(label);
            var textBox = new TextBox
            {
                Location = new Point(_currentX + LABEL_WIDTH + SPACING, _currentY),
                Size = new Size(CONTROL_WIDTH, ThemeManager.Spacing.InputHeight),
                Font = ThemeManager.Fonts.Default
            };

            if (!string.IsNullOrEmpty(placeholder))
            {
                textBox.PlaceholderText = placeholder;
            }

            var timer = new Timer { Interval = 500 };
            timer.Tick += (s, e) =>
            {
                timer.Stop();
                OnFilterChanged(name, textBox.Text);
            };

            textBox.TextChanged += (s, e) =>
            {
                timer.Stop();
                timer.Start();
            };

            AddFilterControl(name, labelControl, textBox);
        }

        /// <summary>
        /// Ajoute un filtre DateTimePicker
        /// </summary>
        /// <param name="name">Nom du filtre</param>
        /// <param name="label">Libellé</param>
        /// <param name="showCheckBox">Afficher la case à cocher</param>
        public void AddDateFilter(string name, string label, bool showCheckBox = true)
        {
            var labelControl = CreateLabel(label);
            var datePicker = new DateTimePicker
            {
                Location = new Point(_currentX + LABEL_WIDTH + SPACING, _currentY),
                Size = new Size(CONTROL_WIDTH, ThemeManager.Spacing.InputHeight),
                Font = ThemeManager.Fonts.Default,
                Format = DateTimePickerFormat.Short,
                ShowCheckBox = showCheckBox
            };

            if (showCheckBox)
            {
                datePicker.Checked = false;
            }

            datePicker.ValueChanged += (s, e) => OnFilterChanged(name, datePicker.Checked ? datePicker.Value : (DateTime?)null);

            AddFilterControl(name, labelControl, datePicker);
        }

        /// <summary>
        /// Ajoute un filtre CheckBox
        /// </summary>
        /// <param name="name">Nom du filtre</param>
        /// <param name="label">Libellé</param>
        /// <param name="defaultChecked">État par défaut</param>
        public void AddCheckBoxFilter(string name, string label, bool defaultChecked = false)
        {
            var checkBox = new CheckBox
            {
                Text = label,
                Location = new Point(_currentX, _currentY + 3),
                Size = new Size(LABEL_WIDTH + CONTROL_WIDTH, 20),
                Font = ThemeManager.Fonts.Default,
                Checked = defaultChecked
            };

            checkBox.CheckedChanged += (s, e) => OnFilterChanged(name, checkBox.Checked);

            _filters[name] = checkBox;
            Controls.Add(checkBox);

            MoveToNextPosition();
        }

        /// <summary>
        /// Ajoute un bouton de réinitialisation
        /// </summary>
        public void AddResetButton()
        {
            var resetButton = ThemeManager.CreateStyledButton("Réinitialiser", ButtonStyle.Secondary);
            resetButton.Location = new Point(_currentX, _currentY);
            resetButton.Size = new Size(100, ThemeManager.Spacing.ButtonHeight);
            resetButton.Click += ResetButton_Click;

            Controls.Add(resetButton);
            MoveToNextPosition();
        }

        /// <summary>
        /// Ajoute un SearchBox
        /// </summary>
        /// <param name="name">Nom du filtre</param>
        /// <param name="label">Libellé</param>
        /// <param name="placeholder">Texte de placeholder</param>
        public void AddSearchBoxFilter(string name, string label, string placeholder = "Rechercher...")
        {
            var labelControl = CreateLabel(label);
            var searchBox = new SearchBox
            {
                Location = new Point(_currentX + LABEL_WIDTH + SPACING, _currentY),
                Size = new Size(CONTROL_WIDTH + 50, 32),
                PlaceholderText = placeholder
            };

            searchBox.Search += (s, e) => OnFilterChanged(name, e.SearchText);

            AddFilterControl(name, labelControl, searchBox);
        }

        private Label CreateLabel(string text)
        {
            return new Label
            {
                Text = text,
                Location = new Point(_currentX, _currentY + 3),
                Size = new Size(LABEL_WIDTH, 20),
                Font = ThemeManager.Fonts.Default,
                ForeColor = ThemeManager.Colors.TextPrimary,
                TextAlign = ContentAlignment.MiddleLeft
            };
        }

        private void AddFilterControl(string name, Label label, Control control)
        {
            _labels[name] = label;
            _filters[name] = control;

            Controls.Add(label);
            Controls.Add(control);

            MoveToNextPosition();
        }

        private void MoveToNextPosition()
        {
            _currentX += LABEL_WIDTH + CONTROL_WIDTH + SPACING * 3;
            
            // Si on dépasse la largeur, passer à la ligne suivante
            if (_currentX + LABEL_WIDTH + CONTROL_WIDTH > Width - 20)
            {
                _currentX = 10;
                _currentY += 35;
                
                // Ajuster la hauteur si nécessaire
                if (_currentY + 35 > Height)
                {
                    Height = _currentY + 45;
                }
            }
        }

        private void ResetButton_Click(object sender, EventArgs e)
        {
            ResetFilters();
        }

        /// <summary>
        /// Réinitialise tous les filtres
        /// </summary>
        public void ResetFilters()
        {
            foreach (var filter in _filters.Values)
            {
                switch (filter)
                {
                    case ComboBox comboBox:
                        if (comboBox.Items.Count > 0)
                            comboBox.SelectedIndex = 0;
                        break;
                    case TextBox textBox:
                        textBox.Clear();
                        break;
                    case DateTimePicker datePicker:
                        if (datePicker.ShowCheckBox)
                            datePicker.Checked = false;
                        break;
                    case CheckBox checkBox:
                        checkBox.Checked = false;
                        break;
                    case SearchBox searchBox:
                        searchBox.Clear();
                        break;
                }
            }

            FiltersReset?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Obtient la valeur d'un filtre
        /// </summary>
        /// <param name="name">Nom du filtre</param>
        /// <returns>Valeur du filtre</returns>
        public object GetFilterValue(string name)
        {
            if (!_filters.ContainsKey(name))
                return null;

            var filter = _filters[name];
            switch (filter)
            {
                case ComboBox comboBox:
                    return comboBox.SelectedItem?.ToString();
                case TextBox textBox:
                    return textBox.Text;
                case DateTimePicker datePicker:
                    return datePicker.ShowCheckBox && !datePicker.Checked ? null : (DateTime?)datePicker.Value;
                case CheckBox checkBox:
                    return checkBox.Checked;
                case SearchBox searchBox:
                    return searchBox.Text;
                default:
                    return null;
            }
        }

        /// <summary>
        /// Définit la valeur d'un filtre
        /// </summary>
        /// <param name="name">Nom du filtre</param>
        /// <param name="value">Valeur à définir</param>
        public void SetFilterValue(string name, object value)
        {
            if (!_filters.ContainsKey(name))
                return;

            var filter = _filters[name];
            switch (filter)
            {
                case ComboBox comboBox:
                    if (value != null && comboBox.Items.Contains(value))
                        comboBox.SelectedItem = value;
                    break;
                case TextBox textBox:
                    textBox.Text = value?.ToString() ?? string.Empty;
                    break;
                case DateTimePicker datePicker:
                    if (value is DateTime dateValue)
                    {
                        datePicker.Value = dateValue;
                        if (datePicker.ShowCheckBox)
                            datePicker.Checked = true;
                    }
                    else if (datePicker.ShowCheckBox)
                    {
                        datePicker.Checked = false;
                    }
                    break;
                case CheckBox checkBox:
                    checkBox.Checked = value is bool boolValue && boolValue;
                    break;
                case SearchBox searchBox:
                    searchBox.Text = value?.ToString() ?? string.Empty;
                    break;
            }
        }

        /// <summary>
        /// Obtient tous les filtres et leurs valeurs
        /// </summary>
        /// <returns>Dictionnaire des filtres</returns>
        public Dictionary<string, object> GetAllFilters()
        {
            var result = new Dictionary<string, object>();
            foreach (var filterName in _filters.Keys)
            {
                result[filterName] = GetFilterValue(filterName);
            }
            return result;
        }

        private void OnFilterChanged(string filterName, object value)
        {
            FilterChanged?.Invoke(this, new FilterChangedEventArgs(filterName, value));
        }
    }

    /// <summary>
    /// Arguments pour l'événement de changement de filtre
    /// </summary>
    public class FilterChangedEventArgs : EventArgs
    {
        public string FilterName { get; }
        public object Value { get; }

        public FilterChangedEventArgs(string filterName, object value)
        {
            FilterName = filterName;
            Value = value;
        }
    }
}
