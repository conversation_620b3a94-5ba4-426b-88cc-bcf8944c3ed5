using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Dapper;

namespace GestionAssociations.Data.Repositories
{
    public abstract class BaseRepository<T> : IRepository<T> where T : class
    {
        protected readonly string _connectionString;
        protected readonly string _tableName;

        protected BaseRepository(string tableName)
        {
            _connectionString = DatabaseConnection.GetConnectionString();
            _tableName = tableName;
        }

        protected IDbConnection CreateConnection()
        {
            return new System.Data.SqlClient.SqlConnection(_connectionString);
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT * FROM {_tableName}";
                return await connection.QueryAsync<T>(sql);
            }
        }

        public virtual async Task<T> GetByIdAsync(int id)
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT * FROM {_tableName} WHERE Id = @Id";
                return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
            }
        }

        public abstract Task<int> AddAsync(T entity);
        public abstract Task<bool> UpdateAsync(T entity);

        public virtual async Task<bool> DeleteAsync(int id)
        {
            using (var connection = CreateConnection())
            {
                var sql = $"DELETE FROM {_tableName} WHERE Id = @Id";
                var affectedRows = await connection.ExecuteAsync(sql, new { Id = id });
                return affectedRows > 0;
            }
        }

        public virtual async Task<int> CountAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT COUNT(*) FROM {_tableName}";
                return await connection.QuerySingleAsync<int>(sql);
            }
        }

        protected async Task<int> ExecuteInsertAsync(string sql, object parameters)
        {
            using (var connection = CreateConnection())
            {
                return await connection.QuerySingleAsync<int>(sql, parameters);
            }
        }

        protected async Task<bool> ExecuteUpdateAsync(string sql, object parameters)
        {
            using (var connection = CreateConnection())
            {
                var affectedRows = await connection.ExecuteAsync(sql, parameters);
                return affectedRows > 0;
            }
        }

        protected async Task<IEnumerable<T>> QueryAsync(string sql, object parameters = null)
        {
            using (var connection = CreateConnection())
            {
                return await connection.QueryAsync<T>(sql, parameters);
            }
        }

        protected async Task<T> QueryFirstOrDefaultAsync(string sql, object parameters = null)
        {
            using (var connection = CreateConnection())
            {
                return await connection.QueryFirstOrDefaultAsync<T>(sql, parameters);
            }
        }
    }
}
