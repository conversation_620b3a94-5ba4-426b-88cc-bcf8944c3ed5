using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Dapper;
using GestionAssociations.Data.Interfaces;

namespace GestionAssociations.Data.Repositories
{
    public abstract class BaseRepository<T> : IRepository<T> where T : class
    {
        protected readonly IConnectionFactory _connectionFactory;
        protected readonly string _tableName;

        protected BaseRepository(IConnectionFactory connectionFactory, string tableName)
        {
            _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
            _tableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
        }

        protected IDbConnection CreateConnection()
        {
            return _connectionFactory.CreateConnection();
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT * FROM {_tableName}";
                return await connection.QueryAsync<T>(sql);
            }
        }

        public virtual async Task<T> GetByIdAsync(int id)
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT * FROM {_tableName} WHERE Id = @Id";
                return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
            }
        }

        public abstract Task<int> CreateAsync(T entity);
        public abstract Task<bool> UpdateAsync(T entity);

        public virtual async Task<PagedResult<T>> GetPagedAsync(int pageNumber, int pageSize)
        {
            using (var connection = CreateConnection())
            {
                var offset = (pageNumber - 1) * pageSize;

                var countSql = $"SELECT COUNT(*) FROM {_tableName}";
                var totalCount = await connection.QuerySingleAsync<int>(countSql);

                var dataSql = $@"
                    SELECT * FROM {_tableName}
                    ORDER BY Id
                    OFFSET @Offset ROWS
                    FETCH NEXT @PageSize ROWS ONLY";

                var items = await connection.QueryAsync<T>(dataSql, new { Offset = offset, PageSize = pageSize });

                return new PagedResult<T>
                {
                    Items = items,
                    TotalCount = totalCount,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                };
            }
        }

        public virtual async Task<bool> ExistsAsync(int id)
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT COUNT(1) FROM {_tableName} WHERE Id = @Id";
                var count = await connection.QuerySingleAsync<int>(sql, new { Id = id });
                return count > 0;
            }
        }

        public virtual async Task<bool> DeleteAsync(int id)
        {
            using (var connection = CreateConnection())
            {
                var sql = $"DELETE FROM {_tableName} WHERE Id = @Id";
                var affectedRows = await connection.ExecuteAsync(sql, new { Id = id });
                return affectedRows > 0;
            }
        }

        public virtual async Task<int> CountAsync()
        {
            using (var connection = CreateConnection())
            {
                var sql = $"SELECT COUNT(*) FROM {_tableName}";
                return await connection.QuerySingleAsync<int>(sql);
            }
        }

        protected async Task<int> ExecuteInsertAsync(string sql, object parameters)
        {
            using (var connection = CreateConnection())
            {
                return await connection.QuerySingleAsync<int>(sql, parameters);
            }
        }

        protected async Task<bool> ExecuteUpdateAsync(string sql, object parameters)
        {
            using (var connection = CreateConnection())
            {
                var affectedRows = await connection.ExecuteAsync(sql, parameters);
                return affectedRows > 0;
            }
        }

        protected async Task<IEnumerable<T>> QueryAsync(string sql, object parameters = null)
        {
            using (var connection = CreateConnection())
            {
                return await connection.QueryAsync<T>(sql, parameters);
            }
        }

        protected async Task<T> QueryFirstOrDefaultAsync(string sql, object parameters = null)
        {
            using (var connection = CreateConnection())
            {
                return await connection.QueryFirstOrDefaultAsync<T>(sql, parameters);
            }
        }
    }
}
