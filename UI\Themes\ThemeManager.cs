using System;
using System.Drawing;
using System.Windows.Forms;

namespace GestionAssociations.UI.Themes
{
    /// <summary>
    /// Gestionnaire de thèmes pour l'application
    /// </summary>
    public static class ThemeManager
    {
        /// <summary>
        /// Couleurs du thème principal
        /// </summary>
        public static class Colors
        {
            // Couleurs principales
            public static Color Primary = Color.FromArgb(70, 70, 100);
            public static Color PrimaryLight = Color.FromArgb(90, 90, 120);
            public static Color PrimaryDark = Color.FromArgb(50, 50, 80);

            // Couleurs secondaires
            public static Color Secondary = Color.FromArgb(240, 240, 240);
            public static Color SecondaryLight = Color.FromArgb(248, 248, 248);
            public static Color SecondaryDark = Color.FromArgb(230, 230, 230);

            // Couleurs d'état
            public static Color Success = Color.FromArgb(40, 167, 69);
            public static Color SuccessLight = Color.FromArgb(60, 187, 89);
            public static Color SuccessDark = Color.FromArgb(20, 147, 49);

            public static Color Danger = Color.FromArgb(220, 53, 69);
            public static Color DangerLight = Color.FromArgb(240, 73, 89);
            public static Color DangerDark = Color.FromArgb(200, 33, 49);

            public static Color Warning = Color.FromArgb(255, 193, 7);
            public static Color WarningLight = Color.FromArgb(255, 213, 47);
            public static Color WarningDark = Color.FromArgb(235, 173, 0);

            public static Color Info = Color.FromArgb(23, 162, 184);
            public static Color InfoLight = Color.FromArgb(43, 182, 204);
            public static Color InfoDark = Color.FromArgb(3, 142, 164);

            // Couleurs de texte
            public static Color TextPrimary = Color.FromArgb(64, 64, 64);
            public static Color TextSecondary = Color.FromArgb(128, 128, 128);
            public static Color TextLight = Color.FromArgb(192, 192, 192);
            public static Color TextWhite = Color.White;

            // Couleurs de fond
            public static Color Background = Color.White;
            public static Color BackgroundLight = Color.FromArgb(248, 249, 250);
            public static Color BackgroundDark = Color.FromArgb(240, 240, 240);

            // Couleurs de bordure
            public static Color Border = Color.FromArgb(200, 200, 200);
            public static Color BorderLight = Color.FromArgb(230, 230, 230);
            public static Color BorderDark = Color.FromArgb(170, 170, 170);
        }

        /// <summary>
        /// Polices du thème
        /// </summary>
        public static class Fonts
        {
            public static Font Default = new Font("Segoe UI", 9F);
            public static Font Small = new Font("Segoe UI", 8F);
            public static Font Large = new Font("Segoe UI", 10F);
            public static Font Title = new Font("Segoe UI", 12F, FontStyle.Bold);
            public static Font Subtitle = new Font("Segoe UI", 10F, FontStyle.Bold);
            public static Font Caption = new Font("Segoe UI", 8F, FontStyle.Italic);
        }

        /// <summary>
        /// Dimensions et espacements
        /// </summary>
        public static class Spacing
        {
            public const int XSmall = 4;
            public const int Small = 8;
            public const int Medium = 16;
            public const int Large = 24;
            public const int XLarge = 32;

            public const int ButtonHeight = 32;
            public const int InputHeight = 24;
            public const int HeaderHeight = 40;
        }

        /// <summary>
        /// Applique le thème à un contrôle et ses enfants
        /// </summary>
        /// <param name="control">Contrôle à thématiser</param>
        public static void ApplyTheme(Control control)
        {
            if (control == null) return;

            // Appliquer le thème selon le type de contrôle
            switch (control)
            {
                case Form form:
                    ApplyFormTheme(form);
                    break;
                case Button button:
                    ApplyButtonTheme(button);
                    break;
                case TextBox textBox:
                    ApplyTextBoxTheme(textBox);
                    break;
                case ComboBox comboBox:
                    ApplyComboBoxTheme(comboBox);
                    break;
                case DataGridView dataGridView:
                    ApplyDataGridViewTheme(dataGridView);
                    break;
                case Label label:
                    ApplyLabelTheme(label);
                    break;
                case Panel panel:
                    ApplyPanelTheme(panel);
                    break;
                case TabControl tabControl:
                    ApplyTabControlTheme(tabControl);
                    break;
            }

            // Appliquer récursivement aux contrôles enfants
            foreach (Control child in control.Controls)
            {
                ApplyTheme(child);
            }
        }

        private static void ApplyFormTheme(Form form)
        {
            form.BackColor = Colors.Background;
            form.ForeColor = Colors.TextPrimary;
            form.Font = Fonts.Default;
        }

        private static void ApplyButtonTheme(Button button)
        {
            button.BackColor = Colors.Primary;
            button.ForeColor = Colors.TextWhite;
            button.Font = Fonts.Default;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Height = Spacing.ButtonHeight;
            button.Cursor = Cursors.Hand;

            // Événements pour les effets hover
            button.MouseEnter += (s, e) => button.BackColor = Colors.PrimaryLight;
            button.MouseLeave += (s, e) => button.BackColor = Colors.Primary;
        }

        private static void ApplyTextBoxTheme(TextBox textBox)
        {
            textBox.BackColor = Colors.Background;
            textBox.ForeColor = Colors.TextPrimary;
            textBox.Font = Fonts.Default;
            textBox.BorderStyle = BorderStyle.FixedSingle;
            textBox.Height = Spacing.InputHeight;
        }

        private static void ApplyComboBoxTheme(ComboBox comboBox)
        {
            comboBox.BackColor = Colors.Background;
            comboBox.ForeColor = Colors.TextPrimary;
            comboBox.Font = Fonts.Default;
            comboBox.FlatStyle = FlatStyle.Flat;
            comboBox.Height = Spacing.InputHeight;
        }

        private static void ApplyDataGridViewTheme(DataGridView dataGridView)
        {
            // Style général
            dataGridView.BackgroundColor = Colors.Background;
            dataGridView.GridColor = Colors.BorderLight;
            dataGridView.BorderStyle = BorderStyle.None;

            // Style des cellules
            dataGridView.DefaultCellStyle.BackColor = Colors.Background;
            dataGridView.DefaultCellStyle.ForeColor = Colors.TextPrimary;
            dataGridView.DefaultCellStyle.SelectionBackColor = Colors.Primary;
            dataGridView.DefaultCellStyle.SelectionForeColor = Colors.TextWhite;
            dataGridView.DefaultCellStyle.Font = Fonts.Default;

            // Style des lignes alternées
            dataGridView.AlternatingRowsDefaultCellStyle.BackColor = Colors.BackgroundLight;

            // Style de l'en-tête
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Colors.Primary;
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Colors.TextWhite;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = Fonts.Subtitle;
            dataGridView.ColumnHeadersHeight = Spacing.HeaderHeight;
            dataGridView.EnableHeadersVisualStyles = false;
        }

        private static void ApplyLabelTheme(Label label)
        {
            label.ForeColor = Colors.TextPrimary;
            label.Font = Fonts.Default;
        }

        private static void ApplyPanelTheme(Panel panel)
        {
            panel.BackColor = Colors.Background;
        }

        private static void ApplyTabControlTheme(TabControl tabControl)
        {
            tabControl.Font = Fonts.Default;
        }

        /// <summary>
        /// Crée un bouton avec un style spécifique
        /// </summary>
        /// <param name="text">Texte du bouton</param>
        /// <param name="style">Style du bouton</param>
        /// <returns>Bouton stylisé</returns>
        public static Button CreateStyledButton(string text, ButtonStyle style = ButtonStyle.Primary)
        {
            var button = new Button
            {
                Text = text,
                Font = Fonts.Default,
                FlatStyle = FlatStyle.Flat,
                Height = Spacing.ButtonHeight,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;

            switch (style)
            {
                case ButtonStyle.Primary:
                    button.BackColor = Colors.Primary;
                    button.ForeColor = Colors.TextWhite;
                    button.MouseEnter += (s, e) => button.BackColor = Colors.PrimaryLight;
                    button.MouseLeave += (s, e) => button.BackColor = Colors.Primary;
                    break;

                case ButtonStyle.Success:
                    button.BackColor = Colors.Success;
                    button.ForeColor = Colors.TextWhite;
                    button.MouseEnter += (s, e) => button.BackColor = Colors.SuccessLight;
                    button.MouseLeave += (s, e) => button.BackColor = Colors.Success;
                    break;

                case ButtonStyle.Danger:
                    button.BackColor = Colors.Danger;
                    button.ForeColor = Colors.TextWhite;
                    button.MouseEnter += (s, e) => button.BackColor = Colors.DangerLight;
                    button.MouseLeave += (s, e) => button.BackColor = Colors.Danger;
                    break;

                case ButtonStyle.Warning:
                    button.BackColor = Colors.Warning;
                    button.ForeColor = Colors.TextPrimary;
                    button.MouseEnter += (s, e) => button.BackColor = Colors.WarningLight;
                    button.MouseLeave += (s, e) => button.BackColor = Colors.Warning;
                    break;

                case ButtonStyle.Secondary:
                    button.BackColor = Colors.Secondary;
                    button.ForeColor = Colors.TextPrimary;
                    button.MouseEnter += (s, e) => button.BackColor = Colors.SecondaryDark;
                    button.MouseLeave += (s, e) => button.BackColor = Colors.Secondary;
                    break;
            }

            return button;
        }

        /// <summary>
        /// Crée un panel avec bordure stylisée
        /// </summary>
        /// <param name="title">Titre du panel</param>
        /// <returns>Panel stylisé</returns>
        public static Panel CreateStyledPanel(string title = null)
        {
            var panel = new Panel
            {
                BackColor = Colors.Background,
                BorderStyle = BorderStyle.FixedSingle
            };

            if (!string.IsNullOrEmpty(title))
            {
                var titleLabel = new Label
                {
                    Text = title,
                    Font = Fonts.Subtitle,
                    ForeColor = Colors.Primary,
                    AutoSize = true,
                    Location = new Point(Spacing.Medium, Spacing.Small)
                };
                panel.Controls.Add(titleLabel);
            }

            return panel;
        }
    }

    /// <summary>
    /// Styles de boutons disponibles
    /// </summary>
    public enum ButtonStyle
    {
        Primary,
        Secondary,
        Success,
        Danger,
        Warning,
        Info
    }
}
