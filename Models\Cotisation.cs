using System;

namespace GestionAssociations.Models
{
    public class Cotisation
    {
        public int Id { get; set; }
        public int AdherentId { get; set; }
        public int ExerciceId { get; set; }
        public decimal Montant { get; set; }
        public DateTime DateEcheance { get; set; }
        public DateTime? DatePaiement { get; set; }
        public string ModePaiement { get; set; }
        public string NumeroCheque { get; set; }
        public string Banque { get; set; }
        public bool EstPayee { get; set; }
        public bool EstEnRetard { get; set; }
        public string Observations { get; set; }
        public DateTime DateCreation { get; set; }
        public DateTime DateModification { get; set; }

        // Propriétés de navigation
        public Adherent Adherent { get; set; }
        public Exercice Exercice { get; set; }
    }

    public class PieceJustificative
    {
        public int Id { get; set; }
        public int AdherentId { get; set; }
        public string TypePiece { get; set; }
        public string NomFichier { get; set; }
        public string CheminFichier { get; set; }
        public DateTime DateUpload { get; set; }
        public DateTime? DateExpiration { get; set; }
        public bool EstValide { get; set; }
        public string Observations { get; set; }

        // Propriété de navigation
        public Adherent Adherent { get; set; }
    }

    public class CertificatMedical
    {
        public int Id { get; set; }
        public int AdherentId { get; set; }
        public DateTime DateCertificat { get; set; }
        public DateTime DateExpiration { get; set; }
        public string MedecinNom { get; set; }
        public string TypeActivite { get; set; }
        public string NomFichier { get; set; }
        public string CheminFichier { get; set; }
        public bool EstValide { get; set; }
        public string Observations { get; set; }
        public DateTime DateCreation { get; set; }

        // Propriété de navigation
        public Adherent Adherent { get; set; }
    }

    public class Exercice
    {
        public int Id { get; set; }
        public string Libelle { get; set; }
        public DateTime DateDebut { get; set; }
        public DateTime DateFin { get; set; }
        public bool EstCourant { get; set; }
        public bool EstCloture { get; set; }
        public DateTime DateCreation { get; set; }
    }
}
