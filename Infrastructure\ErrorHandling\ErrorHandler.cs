using System;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;
using FluentValidation;
using NLog;

namespace GestionAssociations.Infrastructure.ErrorHandling
{
    /// <summary>
    /// Gestionnaire centralisé des erreurs
    /// </summary>
    public static class ErrorHandler
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// Gère une exception et affiche un message approprié à l'utilisateur
        /// </summary>
        /// <param name="ex">Exception à gérer</param>
        /// <param name="context">Contexte de l'erreur</param>
        /// <param name="showToUser">Indique si l'erreur doit être affichée à l'utilisateur</param>
        public static void HandleException(Exception ex, string context = "", bool showToUser = true)
        {
            // Logger l'erreur
            Logger.Error(ex, $"Erreur dans {context}");

            if (!showToUser) return;

            var userMessage = GetUserFriendlyMessage(ex);
            var title = GetErrorTitle(ex);
            var icon = GetErrorIcon(ex);

            MessageBox.Show(userMessage, title, MessageBoxButtons.OK, icon);
        }

        /// <summary>
        /// Gère une exception de validation
        /// </summary>
        /// <param name="ex">Exception de validation</param>
        /// <param name="context">Contexte de l'erreur</param>
        public static void HandleValidationException(ValidationException ex, string context = "")
        {
            Logger.Warn(ex, $"Erreur de validation dans {context}");

            var message = new StringBuilder("Erreurs de validation :\n\n");
            foreach (var error in ex.Errors)
            {
                message.AppendLine($"• {error.ErrorMessage}");
            }

            MessageBox.Show(message.ToString(), "Erreurs de validation", 
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        /// <summary>
        /// Gère une exception avec possibilité de retry
        /// </summary>
        /// <param name="ex">Exception à gérer</param>
        /// <param name="context">Contexte de l'erreur</param>
        /// <param name="retryAction">Action à réessayer</param>
        /// <returns>True si l'utilisateur veut réessayer</returns>
        public static bool HandleExceptionWithRetry(Exception ex, string context, Action retryAction = null)
        {
            Logger.Error(ex, $"Erreur dans {context}");

            var userMessage = GetUserFriendlyMessage(ex);
            var title = GetErrorTitle(ex);

            var result = MessageBox.Show(
                $"{userMessage}\n\nVoulez-vous réessayer ?",
                title,
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Error);

            if (result == DialogResult.Yes && retryAction != null)
            {
                try
                {
                    retryAction();
                    return true;
                }
                catch (Exception retryEx)
                {
                    HandleException(retryEx, $"{context} (retry)");
                    return false;
                }
            }

            return false;
        }

        /// <summary>
        /// Affiche un message d'erreur personnalisé
        /// </summary>
        /// <param name="message">Message à afficher</param>
        /// <param name="title">Titre de la boîte de dialogue</param>
        /// <param name="icon">Icône à afficher</param>
        public static void ShowError(string message, string title = "Erreur", MessageBoxIcon icon = MessageBoxIcon.Error)
        {
            Logger.Error($"Erreur affichée à l'utilisateur: {title} - {message}");
            MessageBox.Show(message, title, MessageBoxButtons.OK, icon);
        }

        /// <summary>
        /// Affiche un message d'avertissement
        /// </summary>
        /// <param name="message">Message à afficher</param>
        /// <param name="title">Titre de la boîte de dialogue</param>
        public static void ShowWarning(string message, string title = "Avertissement")
        {
            Logger.Warn($"Avertissement affiché à l'utilisateur: {title} - {message}");
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        /// <summary>
        /// Affiche un message d'information
        /// </summary>
        /// <param name="message">Message à afficher</param>
        /// <param name="title">Titre de la boîte de dialogue</param>
        public static void ShowInfo(string message, string title = "Information")
        {
            Logger.Info($"Information affichée à l'utilisateur: {title} - {message}");
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Demande une confirmation à l'utilisateur
        /// </summary>
        /// <param name="message">Message de confirmation</param>
        /// <param name="title">Titre de la boîte de dialogue</param>
        /// <returns>True si l'utilisateur confirme</returns>
        public static bool ShowConfirmation(string message, string title = "Confirmation")
        {
            Logger.Info($"Confirmation demandée à l'utilisateur: {title} - {message}");
            var result = MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            return result == DialogResult.Yes;
        }

        /// <summary>
        /// Affiche un message de succès
        /// </summary>
        /// <param name="message">Message de succès</param>
        /// <param name="title">Titre de la boîte de dialogue</param>
        public static void ShowSuccess(string message, string title = "Succès")
        {
            Logger.Info($"Succès affiché à l'utilisateur: {title} - {message}");
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private static string GetUserFriendlyMessage(Exception ex)
        {
            switch (ex)
            {
                case ValidationException validationEx:
                    var sb = new StringBuilder("Erreurs de validation :\n");
                    foreach (var error in validationEx.Errors)
                    {
                        sb.AppendLine($"• {error.ErrorMessage}");
                    }
                    return sb.ToString();

                case SqlException sqlEx:
                    return GetSqlErrorMessage(sqlEx);

                case UnauthorizedAccessException:
                    return "Vous n'avez pas les droits nécessaires pour effectuer cette opération.";

                case TimeoutException:
                    return "L'opération a pris trop de temps. Veuillez réessayer.";

                case ArgumentException argEx:
                    return $"Paramètre invalide : {argEx.Message}";

                case InvalidOperationException invalidOpEx:
                    return $"Opération invalide : {invalidOpEx.Message}";

                case NotSupportedException:
                    return "Cette opération n'est pas supportée.";

                case OutOfMemoryException:
                    return "Mémoire insuffisante pour effectuer cette opération.";

                case System.IO.FileNotFoundException fileNotFoundEx:
                    return $"Fichier non trouvé : {fileNotFoundEx.FileName}";

                case System.IO.DirectoryNotFoundException:
                    return "Répertoire non trouvé.";

                case System.IO.IOException ioEx:
                    return $"Erreur d'entrée/sortie : {ioEx.Message}";

                case System.Net.NetworkInformation.NetworkInformationException:
                    return "Problème de connexion réseau.";

                default:
                    return "Une erreur inattendue s'est produite. Veuillez contacter l'administrateur si le problème persiste.";
            }
        }

        private static string GetSqlErrorMessage(SqlException sqlEx)
        {
            switch (sqlEx.Number)
            {
                case 2: // Timeout
                    return "Délai d'attente dépassé lors de l'accès à la base de données.";
                case 18: // Connection failed
                    return "Impossible de se connecter à la base de données.";
                case 547: // Foreign key constraint
                    return "Impossible de supprimer cet élément car il est utilisé ailleurs.";
                case 2627: // Primary key constraint
                case 2601: // Unique constraint
                    return "Cette valeur existe déjà dans la base de données.";
                case 8152: // String truncation
                    return "Une des valeurs saisies est trop longue.";
                case 515: // Cannot insert null
                    return "Une valeur obligatoire est manquante.";
                case 245: // Conversion failed
                    return "Format de données invalide.";
                case 208: // Invalid object name
                    return "Erreur de structure de base de données.";
                case 207: // Invalid column name
                    return "Erreur de structure de base de données.";
                default:
                    return $"Erreur de base de données : {sqlEx.Message}";
            }
        }

        private static string GetErrorTitle(Exception ex)
        {
            switch (ex)
            {
                case ValidationException:
                    return "Erreurs de validation";
                case SqlException:
                    return "Erreur de base de données";
                case UnauthorizedAccessException:
                    return "Accès refusé";
                case TimeoutException:
                    return "Délai dépassé";
                case System.IO.IOException:
                    return "Erreur de fichier";
                case System.Net.NetworkInformation.NetworkInformationException:
                    return "Erreur réseau";
                default:
                    return "Erreur";
            }
        }

        private static MessageBoxIcon GetErrorIcon(Exception ex)
        {
            switch (ex)
            {
                case ValidationException:
                    return MessageBoxIcon.Warning;
                case UnauthorizedAccessException:
                    return MessageBoxIcon.Stop;
                case TimeoutException:
                    return MessageBoxIcon.Warning;
                default:
                    return MessageBoxIcon.Error;
            }
        }

        /// <summary>
        /// Configure le logging global pour les exceptions non gérées
        /// </summary>
        public static void ConfigureGlobalErrorHandling()
        {
            // Gestion des exceptions non gérées dans l'application Windows Forms
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += Application_ThreadException;

            // Gestion des exceptions non gérées dans les autres threads
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        }

        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            HandleException(e.Exception, "Application Thread Exception", true);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                HandleException(ex, "AppDomain Unhandled Exception", true);
            }
        }
    }
}
