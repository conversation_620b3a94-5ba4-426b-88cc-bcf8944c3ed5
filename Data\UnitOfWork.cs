using System;
using System.Data;
using System.Threading.Tasks;
using GestionAssociations.Data.Interfaces;
using GestionAssociations.Data.Repositories;

namespace GestionAssociations.Data
{
    /// <summary>
    /// Implémentation de l'Unit of Work pattern
    /// </summary>
    public class UnitOfWork : IUnitOfWork
    {
        private readonly IConnectionFactory _connectionFactory;
        private IDbConnection _connection;
        private IDbTransaction _transaction;
        private bool _disposed = false;

        // Repositories
        private IAdherentRepository _adherents;
        private IAssociationRepository _associations;
        private IPoleRepository _poles;
        private ISectionRepository _sections;
        private ITypeAdherentRepository _typesAdherents;
        private IFoyerRepository _foyers;
        private IMedecinRepository _medecins;
        private IPersonneAPrevenirRepository _personnesAPrevenir;
        private ICotisationAdherentRepository _cotisationsAdherents;
        private IFichierAdherentRepository _fichiersAdherents;
        private IEmailAdherentRepository _emailsAdherents;

        public UnitOfWork(IConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory ?? throw new ArgumentNullException(nameof(connectionFactory));
        }

        public IDbConnection Connection
        {
            get
            {
                if (_connection == null)
                {
                    _connection = _connectionFactory.CreateConnection();
                    _connection.Open();
                }
                return _connection;
            }
        }

        public IDbTransaction Transaction => _transaction;

        public IAdherentRepository Adherents
        {
            get { return _adherents ??= new AdherentRepository(_connectionFactory); }
        }

        public IAssociationRepository Associations
        {
            get { return _associations ??= new AssociationRepository(_connectionFactory); }
        }

        public IPoleRepository Poles
        {
            get { return _poles ??= new PoleRepository(_connectionFactory); }
        }

        public ISectionRepository Sections
        {
            get { return _sections ??= new SectionRepository(_connectionFactory); }
        }

        public ITypeAdherentRepository TypesAdherents
        {
            get { return _typesAdherents ??= new TypeAdherentRepository(_connectionFactory); }
        }

        public IFoyerRepository Foyers
        {
            get { return _foyers ??= new FoyerRepository(_connectionFactory); }
        }

        public IMedecinRepository Medecins
        {
            get { return _medecins ??= new MedecinRepository(_connectionFactory); }
        }

        public IPersonneAPrevenirRepository PersonnesAPrevenir
        {
            get { return _personnesAPrevenir ??= new PersonneAPrevenirRepository(_connectionFactory); }
        }

        public ICotisationAdherentRepository CotisationsAdherents
        {
            get { return _cotisationsAdherents ??= new CotisationAdherentRepository(_connectionFactory); }
        }

        public IFichierAdherentRepository FichiersAdherents
        {
            get { return _fichiersAdherents ??= new FichierAdherentRepository(_connectionFactory); }
        }

        public IEmailAdherentRepository EmailsAdherents
        {
            get { return _emailsAdherents ??= new EmailAdherentRepository(_connectionFactory); }
        }

        public async Task<bool> SaveChangesAsync()
        {
            try
            {
                if (_transaction != null)
                {
                    await CommitAsync();
                    return true;
                }
                return true;
            }
            catch
            {
                if (_transaction != null)
                {
                    await RollbackAsync();
                }
                throw;
            }
        }

        public async Task BeginTransactionAsync()
        {
            if (_transaction != null)
            {
                throw new InvalidOperationException("Une transaction est déjà en cours.");
            }

            _transaction = Connection.BeginTransaction();
            await Task.CompletedTask;
        }

        public async Task CommitAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("Aucune transaction en cours.");
            }

            try
            {
                _transaction.Commit();
            }
            finally
            {
                _transaction.Dispose();
                _transaction = null;
            }

            await Task.CompletedTask;
        }

        public async Task RollbackAsync()
        {
            if (_transaction == null)
            {
                throw new InvalidOperationException("Aucune transaction en cours.");
            }

            try
            {
                _transaction.Rollback();
            }
            finally
            {
                _transaction.Dispose();
                _transaction = null;
            }

            await Task.CompletedTask;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _transaction?.Dispose();
                _connection?.Dispose();
                _disposed = true;
            }
        }
    }
}
