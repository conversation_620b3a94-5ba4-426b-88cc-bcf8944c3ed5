using System;
using System.Data;
using System.Threading.Tasks;

namespace GestionAssociations.Data.Interfaces
{
    /// <summary>
    /// Interface pour l'Unit of Work pattern
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        /// <summary>
        /// Repository des adhérents
        /// </summary>
        IAdherentRepository Adherents { get; }

        /// <summary>
        /// Repository des associations
        /// </summary>
        IAssociationRepository Associations { get; }

        /// <summary>
        /// Repository des pôles
        /// </summary>
        IPoleRepository Poles { get; }

        /// <summary>
        /// Repository des sections
        /// </summary>
        ISectionRepository Sections { get; }

        /// <summary>
        /// Repository des types d'adhérents
        /// </summary>
        ITypeAdherentRepository TypesAdherents { get; }

        /// <summary>
        /// Repository des foyers
        /// </summary>
        IFoyerRepository Foyers { get; }

        /// <summary>
        /// Repository des médecins
        /// </summary>
        IMedecinRepository Medecins { get; }

        /// <summary>
        /// Repository des personnes à prévenir
        /// </summary>
        IPersonneAPrevenirRepository PersonnesAPrevenir { get; }

        /// <summary>
        /// Repository des cotisations d'adhérents
        /// </summary>
        ICotisationAdherentRepository CotisationsAdherents { get; }

        /// <summary>
        /// Repository des fichiers d'adhérents
        /// </summary>
        IFichierAdherentRepository FichiersAdherents { get; }

        /// <summary>
        /// Repository des emails d'adhérents
        /// </summary>
        IEmailAdherentRepository EmailsAdherents { get; }

        /// <summary>
        /// Sauvegarde toutes les modifications en attente
        /// </summary>
        /// <returns>True si la sauvegarde a réussi</returns>
        Task<bool> SaveChangesAsync();

        /// <summary>
        /// Démarre une transaction
        /// </summary>
        /// <returns>Task</returns>
        Task BeginTransactionAsync();

        /// <summary>
        /// Valide la transaction en cours
        /// </summary>
        /// <returns>Task</returns>
        Task CommitAsync();

        /// <summary>
        /// Annule la transaction en cours
        /// </summary>
        /// <returns>Task</returns>
        Task RollbackAsync();

        /// <summary>
        /// Obtient la connexion actuelle
        /// </summary>
        IDbConnection Connection { get; }

        /// <summary>
        /// Obtient la transaction actuelle
        /// </summary>
        IDbTransaction Transaction { get; }
    }
}
