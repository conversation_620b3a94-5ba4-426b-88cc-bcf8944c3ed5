using System.Collections.Generic;
using System.Threading.Tasks;
using GestionAssociations.Models;

namespace GestionAssociations.Data.Interfaces
{
    /// <summary>
    /// Interface spécialisée pour le repository des adhérents
    /// </summary>
    public interface IAdherentRepository : IRepository<Adherent>
    {
        /// <summary>
        /// Recherche des adhérents par terme de recherche
        /// </summary>
        /// <param name="searchTerm">Terme de recherche (nom, prénom, email)</param>
        /// <returns>Liste des adhérents correspondants</returns>
        Task<IEnumerable<Adherent>> SearchAsync(string searchTerm);

        /// <summary>
        /// Obtient les adhérents par pôle
        /// </summary>
        /// <param name="poleId">ID du pôle</param>
        /// <returns>Liste des adhérents du pôle</returns>
        Task<IEnumerable<Adherent>> GetByPoleAsync(int poleId);

        /// <summary>
        /// Obtient les adhérents par commune
        /// </summary>
        /// <param name="commune">Nom de la commune</param>
        /// <returns>Liste des adhérents de la commune</returns>
        Task<IEnumerable<Adherent>> GetByCommuneAsync(string commune);

        /// <summary>
        /// Obtient les adhérents par période
        /// </summary>
        /// <param name="periode">Période (ex: "2024-2025")</param>
        /// <returns>Liste des adhérents de la période</returns>
        Task<IEnumerable<Adherent>> GetByPeriodeAsync(string periode);

        /// <summary>
        /// Obtient les adhérents actifs
        /// </summary>
        /// <returns>Liste des adhérents actifs</returns>
        Task<IEnumerable<Adherent>> GetActifsAsync();

        /// <summary>
        /// Obtient les adhérents avec leurs cotisations
        /// </summary>
        /// <param name="adherentId">ID de l'adhérent</param>
        /// <returns>Adhérent avec ses cotisations</returns>
        Task<Adherent> GetWithCotisationsAsync(int adherentId);

        /// <summary>
        /// Obtient les adhérents avec leurs personnes à prévenir
        /// </summary>
        /// <param name="adherentId">ID de l'adhérent</param>
        /// <returns>Adhérent avec ses personnes à prévenir</returns>
        Task<Adherent> GetWithPersonnesAPrevenirAsync(int adherentId);

        /// <summary>
        /// Met à jour la photo d'un adhérent
        /// </summary>
        /// <param name="adherentId">ID de l'adhérent</param>
        /// <param name="photo">Données de la photo</param>
        /// <returns>True si la mise à jour a réussi</returns>
        Task<bool> UpdatePhotoAsync(int adherentId, byte[] photo);

        /// <summary>
        /// Obtient les statistiques des adhérents
        /// </summary>
        /// <returns>Statistiques des adhérents</returns>
        Task<AdherentStats> GetStatsAsync();

        /// <summary>
        /// Recherche avancée avec filtres multiples
        /// </summary>
        /// <param name="filters">Filtres de recherche</param>
        /// <returns>Résultat paginé des adhérents</returns>
        Task<PagedResult<Adherent>> SearchAdvancedAsync(AdherentSearchFilters filters);
    }

    /// <summary>
    /// Statistiques des adhérents
    /// </summary>
    public class AdherentStats
    {
        public int TotalAdherents { get; set; }
        public int AdherentsActifs { get; set; }
        public int AdherentsInactifs { get; set; }
        public int AdherentsHommes { get; set; }
        public int AdherentsFemmes { get; set; }
        public Dictionary<string, int> AdherentsParPole { get; set; } = new Dictionary<string, int>();
        public Dictionary<string, int> AdherentsParSection { get; set; } = new Dictionary<string, int>();
    }

    /// <summary>
    /// Filtres de recherche pour les adhérents
    /// </summary>
    public class AdherentSearchFilters
    {
        public string SearchTerm { get; set; }
        public int? PoleId { get; set; }
        public string Commune { get; set; }
        public string Periode { get; set; }
        public bool? EstActif { get; set; }
        public string Civilite { get; set; }
        public int? AgeMin { get; set; }
        public int? AgeMax { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }
}
