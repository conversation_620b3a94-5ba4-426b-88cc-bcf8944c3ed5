<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="logs/internal-nlog.txt">

  <!-- Définition des cibles de logging -->
  <targets>
    <!-- Fichier de log principal -->
    <target xsi:type="File" 
            name="fileTarget"
            fileName="logs/${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}"
            archiveFileName="logs/archive/log.{#}.txt"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false" />

    <!-- Fichier de log pour les erreurs uniquement -->
    <target xsi:type="File" 
            name="errorFileTarget"
            fileName="logs/errors/${shortdate}-errors.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}"
            archiveFileName="logs/errors/archive/error.{#}.txt"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="90"
            concurrentWrites="true"
            keepFileOpen="false" />

    <!-- Console pour le développement -->
    <target xsi:type="Console" 
            name="consoleTarget"
            layout="${time} ${level:uppercase=true} ${logger:shortName=true} ${message} ${exception:format=shortType}" />

    <!-- Base de données pour les logs critiques -->
    <target xsi:type="Database" 
            name="databaseTarget"
            connectionString="${configsetting:name=ConnectionStrings.DefaultConnection}"
            commandText="INSERT INTO Logs(Date, Level, Logger, Message, Exception, MachineName, UserName) VALUES(@Date, @Level, @Logger, @Message, @Exception, @MachineName, @UserName)">
      <parameter name="@Date" layout="${date}" />
      <parameter name="@Level" layout="${level}" />
      <parameter name="@Logger" layout="${logger}" />
      <parameter name="@Message" layout="${message}" />
      <parameter name="@Exception" layout="${exception:tostring}" />
      <parameter name="@MachineName" layout="${machinename}" />
      <parameter name="@UserName" layout="${windows-identity}" />
    </target>

    <!-- Fichier de log pour les performances -->
    <target xsi:type="File" 
            name="performanceTarget"
            fileName="logs/performance/${shortdate}-performance.log"
            layout="${longdate} ${message}"
            archiveFileName="logs/performance/archive/perf.{#}.txt"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="7"
            concurrentWrites="true"
            keepFileOpen="false" />

    <!-- Fichier de log pour l'audit -->
    <target xsi:type="File" 
            name="auditTarget"
            fileName="logs/audit/${shortdate}-audit.log"
            layout="${longdate} ${windows-identity} ${message}"
            archiveFileName="logs/audit/archive/audit.{#}.txt"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="365"
            concurrentWrites="true"
            keepFileOpen="false" />
  </targets>

  <!-- Règles de logging -->
  <rules>
    <!-- Logs de performance -->
    <logger name="Performance.*" minlevel="Info" writeTo="performanceTarget" final="true" />
    
    <!-- Logs d'audit -->
    <logger name="Audit.*" minlevel="Info" writeTo="auditTarget" final="true" />
    
    <!-- Erreurs critiques vers la base de données -->
    <logger name="*" minlevel="Error" writeTo="databaseTarget" />
    
    <!-- Toutes les erreurs vers le fichier d'erreurs -->
    <logger name="*" minlevel="Warn" writeTo="errorFileTarget" />
    
    <!-- Tous les logs vers le fichier principal -->
    <logger name="*" minlevel="Info" writeTo="fileTarget" />
    
    <!-- Console pour le développement (niveau Debug) -->
    <logger name="*" minlevel="Debug" writeTo="consoleTarget" />
  </rules>
</nlog>
