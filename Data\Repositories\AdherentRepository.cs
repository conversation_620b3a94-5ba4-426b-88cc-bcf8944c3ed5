using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using GestionAssociations.Data.Interfaces;
using GestionAssociations.Models;

namespace GestionAssociations.Data.Repositories
{
    /// <summary>
    /// Repository spécialisé pour les adhérents
    /// </summary>
    public class AdherentRepository : BaseRepository<Adherent>, IAdherentRepository
    {
        public AdherentRepository(IConnectionFactory connectionFactory) 
            : base(connectionFactory, "Adherents")
        {
        }

        public override async Task<int> CreateAsync(Adherent entity)
        {
            using var connection = CreateConnection();
            
            var sql = @"
                INSERT INTO Adherents (
                    NumeroAdherent, RaisonSociale, Civilite, Nom, Prenom, RepresentantLegal,
                    DateNaissance, LieuNaissance, Nationalite, NonArchive, DerniereMiseAJour,
                    TypeAdresse1, ComplementAdresse1, NumeroVoie1, NomVoie1, CodePostal1, Ville1, Pays1,
                    Email1, TelDomicile1, TelMobile1, TelBureau1, Fax1, AdresseErronee1,
                    TypeAdresse2, ComplementAdresse2, NumeroVoie2, NomVoie2, CodePostal2, Ville2, Pays2,
                    Email2, TelDomicile2, TelMobile2, TelBureau2, Fax2, AdresseErronee2,
                    EnvoyerCourrierAdresse1, EnvoyerCourrierAdresse2, EnvoyerCourrierLesDeux,
                    EnvoyerEmailAdresse1, EnvoyerEmailAdresse2, EnvoyerEmailLesDeux,
                    EnvoyerCourriers, ImprimerEtiquettes, EnvoyerEmails,
                    DateAdhesion, TypeAdherent2024_2025, NumeroLicence1, NumeroLicence2,
                    DateDemission, DateEviction, PoleSelection,
                    ChefDeFamille, NbPaiementsParDefaut, MoyenPaiementPrefere, BanqueAdherent,
                    MontantARegler, MontantPaye, DatePaiement,
                    GererCertificatMedical, MedecinTraitantId,
                    Profession, Employeur, EstActif, DateInscription, DateRadiation, MotifRadiation,
                    TypeAdherentId, SectionId, FoyerId, Observations, DateCreation, DateModification
                )
                VALUES (
                    @NumeroAdherent, @RaisonSociale, @Civilite, @Nom, @Prenom, @RepresentantLegal,
                    @DateNaissance, @LieuNaissance, @Nationalite, @NonArchive, @DerniereMiseAJour,
                    @TypeAdresse1, @ComplementAdresse1, @NumeroVoie1, @NomVoie1, @CodePostal1, @Ville1, @Pays1,
                    @Email1, @TelDomicile1, @TelMobile1, @TelBureau1, @Fax1, @AdresseErronee1,
                    @TypeAdresse2, @ComplementAdresse2, @NumeroVoie2, @NomVoie2, @CodePostal2, @Ville2, @Pays2,
                    @Email2, @TelDomicile2, @TelMobile2, @TelBureau2, @Fax2, @AdresseErronee2,
                    @EnvoyerCourrierAdresse1, @EnvoyerCourrierAdresse2, @EnvoyerCourrierLesDeux,
                    @EnvoyerEmailAdresse1, @EnvoyerEmailAdresse2, @EnvoyerEmailLesDeux,
                    @EnvoyerCourriers, @ImprimerEtiquettes, @EnvoyerEmails,
                    @DateAdhesion, @TypeAdherent2024_2025, @NumeroLicence1, @NumeroLicence2,
                    @DateDemission, @DateEviction, @PoleSelection,
                    @ChefDeFamille, @NbPaiementsParDefaut, @MoyenPaiementPrefere, @BanqueAdherent,
                    @MontantARegler, @MontantPaye, @DatePaiement,
                    @GererCertificatMedical, @MedecinTraitantId,
                    @Profession, @Employeur, @EstActif, @DateInscription, @DateRadiation, @MotifRadiation,
                    @TypeAdherentId, @SectionId, @FoyerId, @Observations, @DateCreation, @DateModification
                );
                SELECT CAST(SCOPE_IDENTITY() as int);";

            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Adherent entity)
        {
            using var connection = CreateConnection();
            
            var sql = @"
                UPDATE Adherents SET
                    NumeroAdherent = @NumeroAdherent,
                    RaisonSociale = @RaisonSociale,
                    Civilite = @Civilite,
                    Nom = @Nom,
                    Prenom = @Prenom,
                    RepresentantLegal = @RepresentantLegal,
                    DateNaissance = @DateNaissance,
                    LieuNaissance = @LieuNaissance,
                    Nationalite = @Nationalite,
                    NonArchive = @NonArchive,
                    DerniereMiseAJour = @DerniereMiseAJour,
                    TypeAdresse1 = @TypeAdresse1,
                    ComplementAdresse1 = @ComplementAdresse1,
                    NumeroVoie1 = @NumeroVoie1,
                    NomVoie1 = @NomVoie1,
                    CodePostal1 = @CodePostal1,
                    Ville1 = @Ville1,
                    Pays1 = @Pays1,
                    Email1 = @Email1,
                    TelDomicile1 = @TelDomicile1,
                    TelMobile1 = @TelMobile1,
                    TelBureau1 = @TelBureau1,
                    Fax1 = @Fax1,
                    AdresseErronee1 = @AdresseErronee1,
                    TypeAdresse2 = @TypeAdresse2,
                    ComplementAdresse2 = @ComplementAdresse2,
                    NumeroVoie2 = @NumeroVoie2,
                    NomVoie2 = @NomVoie2,
                    CodePostal2 = @CodePostal2,
                    Ville2 = @Ville2,
                    Pays2 = @Pays2,
                    Email2 = @Email2,
                    TelDomicile2 = @TelDomicile2,
                    TelMobile2 = @TelMobile2,
                    TelBureau2 = @TelBureau2,
                    Fax2 = @Fax2,
                    AdresseErronee2 = @AdresseErronee2,
                    EnvoyerCourrierAdresse1 = @EnvoyerCourrierAdresse1,
                    EnvoyerCourrierAdresse2 = @EnvoyerCourrierAdresse2,
                    EnvoyerCourrierLesDeux = @EnvoyerCourrierLesDeux,
                    EnvoyerEmailAdresse1 = @EnvoyerEmailAdresse1,
                    EnvoyerEmailAdresse2 = @EnvoyerEmailAdresse2,
                    EnvoyerEmailLesDeux = @EnvoyerEmailLesDeux,
                    EnvoyerCourriers = @EnvoyerCourriers,
                    ImprimerEtiquettes = @ImprimerEtiquettes,
                    EnvoyerEmails = @EnvoyerEmails,
                    DateAdhesion = @DateAdhesion,
                    TypeAdherent2024_2025 = @TypeAdherent2024_2025,
                    NumeroLicence1 = @NumeroLicence1,
                    NumeroLicence2 = @NumeroLicence2,
                    DateDemission = @DateDemission,
                    DateEviction = @DateEviction,
                    PoleSelection = @PoleSelection,
                    ChefDeFamille = @ChefDeFamille,
                    NbPaiementsParDefaut = @NbPaiementsParDefaut,
                    MoyenPaiementPrefere = @MoyenPaiementPrefere,
                    BanqueAdherent = @BanqueAdherent,
                    MontantARegler = @MontantARegler,
                    MontantPaye = @MontantPaye,
                    DatePaiement = @DatePaiement,
                    GererCertificatMedical = @GererCertificatMedical,
                    MedecinTraitantId = @MedecinTraitantId,
                    Profession = @Profession,
                    Employeur = @Employeur,
                    EstActif = @EstActif,
                    DateInscription = @DateInscription,
                    DateRadiation = @DateRadiation,
                    MotifRadiation = @MotifRadiation,
                    TypeAdherentId = @TypeAdherentId,
                    SectionId = @SectionId,
                    FoyerId = @FoyerId,
                    Observations = @Observations,
                    DateModification = @DateModification
                WHERE Id = @Id";

            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<IEnumerable<Adherent>> SearchAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllAsync();

            using var connection = CreateConnection();
            
            var sql = @"
                SELECT a.*, t.Libelle as TypeLibelle, s.Libelle as SectionLibelle
                FROM Adherents a
                LEFT JOIN TypesAdherents t ON a.TypeAdherentId = t.Id
                LEFT JOIN Sections s ON a.SectionId = s.Id
                WHERE a.EstActif = 1 
                AND (
                    a.Nom LIKE @SearchTerm 
                    OR a.Prenom LIKE @SearchTerm 
                    OR a.Email1 LIKE @SearchTerm 
                    OR a.Email2 LIKE @SearchTerm
                    OR a.RaisonSociale LIKE @SearchTerm
                )
                ORDER BY a.Nom, a.Prenom";

            var searchPattern = $"%{searchTerm}%";
            return await connection.QueryAsync<Adherent, TypeAdherent, Section, Adherent>(
                sql,
                (adherent, type, section) =>
                {
                    adherent.TypeAdherent = type;
                    adherent.Section = section;
                    return adherent;
                },
                new { SearchTerm = searchPattern },
                splitOn: "TypeLibelle,SectionLibelle"
            );
        }

        public async Task<IEnumerable<Adherent>> GetByPoleAsync(int poleId)
        {
            using var connection = CreateConnection();
            
            var sql = @"
                SELECT a.*, t.Libelle as TypeLibelle, s.Libelle as SectionLibelle
                FROM Adherents a
                LEFT JOIN TypesAdherents t ON a.TypeAdherentId = t.Id
                LEFT JOIN Sections s ON a.SectionId = s.Id
                WHERE a.EstActif = 1 AND s.PoleId = @PoleId
                ORDER BY a.Nom, a.Prenom";

            return await connection.QueryAsync<Adherent, TypeAdherent, Section, Adherent>(
                sql,
                (adherent, type, section) =>
                {
                    adherent.TypeAdherent = type;
                    adherent.Section = section;
                    return adherent;
                },
                new { PoleId = poleId },
                splitOn: "TypeLibelle,SectionLibelle"
            );
        }

        public async Task<IEnumerable<Adherent>> GetByCommuneAsync(string commune)
        {
            if (string.IsNullOrWhiteSpace(commune))
                return await GetActifsAsync();

            using var connection = CreateConnection();
            
            var sql = @"
                SELECT a.*, t.Libelle as TypeLibelle, s.Libelle as SectionLibelle
                FROM Adherents a
                LEFT JOIN TypesAdherents t ON a.TypeAdherentId = t.Id
                LEFT JOIN Sections s ON a.SectionId = s.Id
                WHERE a.EstActif = 1 AND (a.Ville1 = @Commune OR a.Ville2 = @Commune)
                ORDER BY a.Nom, a.Prenom";

            return await connection.QueryAsync<Adherent, TypeAdherent, Section, Adherent>(
                sql,
                (adherent, type, section) =>
                {
                    adherent.TypeAdherent = type;
                    adherent.Section = section;
                    return adherent;
                },
                new { Commune = commune },
                splitOn: "TypeLibelle,SectionLibelle"
            );
        }

        public async Task<IEnumerable<Adherent>> GetByPeriodeAsync(string periode)
        {
            using var connection = CreateConnection();
            
            var sql = @"
                SELECT a.*, t.Libelle as TypeLibelle, s.Libelle as SectionLibelle
                FROM Adherents a
                LEFT JOIN TypesAdherents t ON a.TypeAdherentId = t.Id
                LEFT JOIN Sections s ON a.SectionId = s.Id
                WHERE a.EstActif = 1 AND a.TypeAdherent2024_2025 = @Periode
                ORDER BY a.Nom, a.Prenom";

            return await connection.QueryAsync<Adherent, TypeAdherent, Section, Adherent>(
                sql,
                (adherent, type, section) =>
                {
                    adherent.TypeAdherent = type;
                    adherent.Section = section;
                    return adherent;
                },
                new { Periode = periode },
                splitOn: "TypeLibelle,SectionLibelle"
            );
        }

        public async Task<IEnumerable<Adherent>> GetActifsAsync()
        {
            using var connection = CreateConnection();
            
            var sql = @"
                SELECT a.*, t.Libelle as TypeLibelle, s.Libelle as SectionLibelle
                FROM Adherents a
                LEFT JOIN TypesAdherents t ON a.TypeAdherentId = t.Id
                LEFT JOIN Sections s ON a.SectionId = s.Id
                WHERE a.EstActif = 1
                ORDER BY a.Nom, a.Prenom";

            return await connection.QueryAsync<Adherent, TypeAdherent, Section, Adherent>(
                sql,
                (adherent, type, section) =>
                {
                    adherent.TypeAdherent = type;
                    adherent.Section = section;
                    return adherent;
                },
                splitOn: "TypeLibelle,SectionLibelle"
            );
        }

        public async Task<Adherent> GetWithCotisationsAsync(int adherentId)
        {
            using var connection = CreateConnection();
            
            var sql = @"
                SELECT a.*, c.*
                FROM Adherents a
                LEFT JOIN CotisationsAdherents c ON a.Id = c.AdherentId
                WHERE a.Id = @AdherentId";

            var adherentDict = new Dictionary<int, Adherent>();
            
            await connection.QueryAsync<Adherent, CotisationAdherent, Adherent>(
                sql,
                (adherent, cotisation) =>
                {
                    if (!adherentDict.TryGetValue(adherent.Id, out var adherentEntry))
                    {
                        adherentEntry = adherent;
                        adherentEntry.Cotisations = new List<CotisationAdherent>();
                        adherentDict.Add(adherent.Id, adherentEntry);
                    }

                    if (cotisation != null)
                        adherentEntry.Cotisations.Add(cotisation);

                    return adherentEntry;
                },
                new { AdherentId = adherentId },
                splitOn: "Id"
            );

            return adherentDict.Values.FirstOrDefault();
        }

        public async Task<Adherent> GetWithPersonnesAPrevenirAsync(int adherentId)
        {
            using var connection = CreateConnection();
            
            var sql = @"
                SELECT a.*, p.*
                FROM Adherents a
                LEFT JOIN PersonnesAPrevenir p ON a.Id = p.AdherentId
                WHERE a.Id = @AdherentId";

            var adherentDict = new Dictionary<int, Adherent>();
            
            await connection.QueryAsync<Adherent, PersonneAPrevenir, Adherent>(
                sql,
                (adherent, personne) =>
                {
                    if (!adherentDict.TryGetValue(adherent.Id, out var adherentEntry))
                    {
                        adherentEntry = adherent;
                        adherentEntry.PersonnesAPrevenir = new List<PersonneAPrevenir>();
                        adherentDict.Add(adherent.Id, adherentEntry);
                    }

                    if (personne != null)
                        adherentEntry.PersonnesAPrevenir.Add(personne);

                    return adherentEntry;
                },
                new { AdherentId = adherentId },
                splitOn: "Id"
            );

            return adherentDict.Values.FirstOrDefault();
        }

        public async Task<bool> UpdatePhotoAsync(int adherentId, byte[] photo)
        {
            using var connection = CreateConnection();
            
            var sql = "UPDATE Adherents SET Photo = @Photo WHERE Id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, new { Id = adherentId, Photo = photo });
            return affectedRows > 0;
        }

        public async Task<AdherentStats> GetStatsAsync()
        {
            using var connection = CreateConnection();
            
            var stats = new AdherentStats();
            
            // Statistiques générales
            var generalStats = await connection.QuerySingleAsync<dynamic>(@"
                SELECT 
                    COUNT(*) as TotalAdherents,
                    SUM(CASE WHEN EstActif = 1 THEN 1 ELSE 0 END) as AdherentsActifs,
                    SUM(CASE WHEN EstActif = 0 THEN 1 ELSE 0 END) as AdherentsInactifs,
                    SUM(CASE WHEN Civilite = 'Masculin' THEN 1 ELSE 0 END) as AdherentsHommes,
                    SUM(CASE WHEN Civilite = 'Féminin' THEN 1 ELSE 0 END) as AdherentsFemmes
                FROM Adherents");

            stats.TotalAdherents = generalStats.TotalAdherents;
            stats.AdherentsActifs = generalStats.AdherentsActifs;
            stats.AdherentsInactifs = generalStats.AdherentsInactifs;
            stats.AdherentsHommes = generalStats.AdherentsHommes;
            stats.AdherentsFemmes = generalStats.AdherentsFemmes;

            // Statistiques par pôle
            var poleStats = await connection.QueryAsync<dynamic>(@"
                SELECT p.Libelle, COUNT(a.Id) as Nombre
                FROM Poles p
                LEFT JOIN Sections s ON p.Id = s.PoleId
                LEFT JOIN Adherents a ON s.Id = a.SectionId AND a.EstActif = 1
                GROUP BY p.Id, p.Libelle");

            foreach (var stat in poleStats)
            {
                stats.AdherentsParPole[stat.Libelle] = stat.Nombre;
            }

            // Statistiques par section
            var sectionStats = await connection.QueryAsync<dynamic>(@"
                SELECT s.Libelle, COUNT(a.Id) as Nombre
                FROM Sections s
                LEFT JOIN Adherents a ON s.Id = a.SectionId AND a.EstActif = 1
                GROUP BY s.Id, s.Libelle");

            foreach (var stat in sectionStats)
            {
                stats.AdherentsParSection[stat.Libelle] = stat.Nombre;
            }

            return stats;
        }

        public async Task<PagedResult<Adherent>> SearchAdvancedAsync(AdherentSearchFilters filters)
        {
            using var connection = CreateConnection();
            
            var whereConditions = new List<string> { "1=1" };
            var parameters = new DynamicParameters();

            // Filtres
            if (!string.IsNullOrWhiteSpace(filters.SearchTerm))
            {
                whereConditions.Add("(a.Nom LIKE @SearchTerm OR a.Prenom LIKE @SearchTerm OR a.Email1 LIKE @SearchTerm OR a.Email2 LIKE @SearchTerm)");
                parameters.Add("SearchTerm", $"%{filters.SearchTerm}%");
            }

            if (filters.PoleId.HasValue)
            {
                whereConditions.Add("s.PoleId = @PoleId");
                parameters.Add("PoleId", filters.PoleId.Value);
            }

            if (!string.IsNullOrWhiteSpace(filters.Commune))
            {
                whereConditions.Add("(a.Ville1 = @Commune OR a.Ville2 = @Commune)");
                parameters.Add("Commune", filters.Commune);
            }

            if (!string.IsNullOrWhiteSpace(filters.Periode))
            {
                whereConditions.Add("a.TypeAdherent2024_2025 = @Periode");
                parameters.Add("Periode", filters.Periode);
            }

            if (filters.EstActif.HasValue)
            {
                whereConditions.Add("a.EstActif = @EstActif");
                parameters.Add("EstActif", filters.EstActif.Value);
            }

            if (!string.IsNullOrWhiteSpace(filters.Civilite))
            {
                whereConditions.Add("a.Civilite = @Civilite");
                parameters.Add("Civilite", filters.Civilite);
            }

            var whereClause = string.Join(" AND ", whereConditions);
            
            // Compter le total
            var countSql = $@"
                SELECT COUNT(*)
                FROM Adherents a
                LEFT JOIN Sections s ON a.SectionId = s.Id
                WHERE {whereClause}";
            
            var totalCount = await connection.QuerySingleAsync<int>(countSql, parameters);
            
            // Récupérer les données paginées
            var offset = (filters.PageNumber - 1) * filters.PageSize;
            parameters.Add("Offset", offset);
            parameters.Add("PageSize", filters.PageSize);
            
            var dataSql = $@"
                SELECT a.*, t.Libelle as TypeLibelle, s.Libelle as SectionLibelle
                FROM Adherents a
                LEFT JOIN TypesAdherents t ON a.TypeAdherentId = t.Id
                LEFT JOIN Sections s ON a.SectionId = s.Id
                WHERE {whereClause}
                ORDER BY a.Nom, a.Prenom
                OFFSET @Offset ROWS
                FETCH NEXT @PageSize ROWS ONLY";
            
            var items = await connection.QueryAsync<Adherent, TypeAdherent, Section, Adherent>(
                dataSql,
                (adherent, type, section) =>
                {
                    adherent.TypeAdherent = type;
                    adherent.Section = section;
                    return adherent;
                },
                parameters,
                splitOn: "TypeLibelle,SectionLibelle"
            );
            
            return new PagedResult<Adherent>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = filters.PageNumber,
                PageSize = filters.PageSize
            };
        }
    }
}
