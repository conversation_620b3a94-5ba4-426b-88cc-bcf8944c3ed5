# Modules et Sous-menus - Application Gestion des Associations

## 1. ASSOCIATION
Module principal pour la gestion de l'association

### Sous-menus:
- **Accueil** - Page d'accueil principale
- **Tableau de bord** - Dashboard avec statistiques
- **Stockage sur serveur** - Gestion du stockage
- **Description association** - Informations sur l'association
- **Gestion du prise** - Gestion des prises
- **Membres du bureau** - Gestion des membres du bureau
- **Signatures** - Gestion des signatures
- **Droits utilisateurs** - Gestion des droits d'accès
- **Modifier mot de passe** - Changement de mot de passe

## 2. ADHÉRENTS
Module pour la gestion des adhérents

### Sous-menus:
- **Adhérents** - Liste et gestion des adhérents
- **Listes de diffusion** - Gestion des listes de diffusion
- **Types d'adhérents** - Classification des types d'adhérents
- **Personnaliser** - Personnalisation des paramètres adhérents
- **Stats adhérents** - Statistiques des adhérents
- **Import** - Importation de données adhérents
- **Export** - Exportation de données adhérents
- **Sections** - Gestion des sections
- **Stats sections** - Statistiques par section
- **Cotisations** - Gestion des cotisations
- **Pièces justificatives** - Gestion des documents
- **Certificats médicaux** - Gestion des certificats médicaux
- **Foyers** - Gestion des foyers familiaux

## 3. COMPTABILITÉ
Module pour la gestion comptable

### Sous-menus:
- **Relevé de compte** - Consultation des relevés
- **Comptes bancaires** - Gestion des comptes bancaires
- **Chéquiers** - Gestion des chéquiers
- **Échéancier** - Gestion des échéances
- **Plan comptable** - Plan comptable de l'association
- **Tiers** - Gestion des tiers
- **Événements** - Événements comptables
- **Import** - Importation de données comptables
- **Compte résultat** - Compte de résultat
- **Bilan par catégorie** - Bilan catégorisé
- **Bilan par événement** - Bilan par événement
- **Budgets** - Gestion des budgets
- **Dons** - Gestion des dons

## 4. ORGANISER
Module pour l'organisation d'événements

### Sous-menus:
- **Activités** - Gestion des activités
- **Inscriptions activités** - Inscriptions aux activités
- **Participants activités** - Gestion des participants
- **Manifestation** - Organisation de manifestations
- **Inscriptions manifestation** - Inscriptions aux manifestations
- **Participants manifestation** - Participants aux manifestations
- **Plan type** - Modèles de plans
- **Personnaliser** - Personnalisation des événements
- **Suivi des avoirs** - Suivi des avoirs clients

## 5. SECRÉTARIAT
Module pour les tâches de secrétariat

### Sous-menus:
- **Courriers** - Gestion du courrier
- **Étiquettes** - Création d'étiquettes
- **Publipostage** - Publipostage et mailing
- **Contacts** - Gestion des contacts
- **Import contacts** - Importation de contacts
- **Emails** - Gestion des emails
- **Modèles emails** - Modèles d'emails
- **SMS** - Envoi de SMS
- **Bons d'achat** - Gestion des bons d'achat
- **Édition hors statuts** - Éditions spéciales
- **Rembourser hors statuts** - Remboursements spéciaux
- **Stats hors statuts** - Statistiques spéciales

## 6. MATÉRIELS
Module pour la gestion du matériel

### Sous-menus:
- **Gestion matériel** - Inventaire et gestion du matériel
- **Entrées** - Entrées de matériel
- **Sorties** - Sorties de matériel
- **Suivi des PBs** - Suivi des problèmes
- **Personnaliser** - Personnalisation matériel
- **Stats matériels** - Statistiques du matériel
- **Import matériels** - Importation de données matériel

---

## Architecture de l'Application

### Technologies utilisées:
- **Framework**: .NET Framework 4.8
- **Interface**: Windows Forms
- **ORM**: Dapper
- **Base de données**: SQL Server (recommandé)
- **Langage**: C#

### Structure recommandée:
```
GestionAssociations/
├── Forms/
│   ├── Association/
│   ├── Adherents/
│   ├── Comptabilite/
│   ├── Organiser/
│   ├── Secretariat/
│   └── Materiels/
├── Models/
├── Services/
├── Data/
└── Utils/
```
