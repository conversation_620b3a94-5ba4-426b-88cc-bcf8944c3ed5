using System.Collections.Generic;
using System.Threading.Tasks;
using GestionAssociations.Models;

namespace GestionAssociations.Data.Interfaces
{
    /// <summary>
    /// Interface pour le repository des pôles
    /// </summary>
    public interface IPoleRepository : IRepository<Pole>
    {
        Task<IEnumerable<Pole>> GetActifsAsync();
    }

    /// <summary>
    /// Interface pour le repository des sections
    /// </summary>
    public interface ISectionRepository : IRepository<Section>
    {
        Task<IEnumerable<Section>> GetByPoleAsync(int poleId);
        Task<IEnumerable<Section>> GetActivesAsync();
    }

    /// <summary>
    /// Interface pour le repository des types d'adhérents
    /// </summary>
    public interface ITypeAdherentRepository : IRepository<TypeAdherent>
    {
        Task<IEnumerable<TypeAdherent>> GetActifsAsync();
    }

    /// <summary>
    /// Interface pour le repository des foyers
    /// </summary>
    public interface IFoyerRepository : IRepository<Foyer>
    {
        Task<IEnumerable<Foyer>> GetActifsAsync();
    }

    /// <summary>
    /// Interface pour le repository des médecins
    /// </summary>
    public interface IMedecinRepository : IRepository<Medecin>
    {
        Task<IEnumerable<Medecin>> GetActifsAsync();
        Task<IEnumerable<Medecin>> SearchAsync(string searchTerm);
    }

    /// <summary>
    /// Interface pour le repository des personnes à prévenir
    /// </summary>
    public interface IPersonneAPrevenirRepository : IRepository<PersonneAPrevenir>
    {
        Task<IEnumerable<PersonneAPrevenir>> GetByAdherentAsync(int adherentId);
    }

    /// <summary>
    /// Interface pour le repository des cotisations d'adhérents
    /// </summary>
    public interface ICotisationAdherentRepository : IRepository<CotisationAdherent>
    {
        Task<IEnumerable<CotisationAdherent>> GetByAdherentAsync(int adherentId);
        Task<IEnumerable<CotisationAdherent>> GetByPeriodeAsync(string periode);
    }

    /// <summary>
    /// Interface pour le repository des fichiers d'adhérents
    /// </summary>
    public interface IFichierAdherentRepository : IRepository<FichierAdherent>
    {
        Task<IEnumerable<FichierAdherent>> GetByAdherentAsync(int adherentId);
    }

    /// <summary>
    /// Interface pour le repository des emails d'adhérents
    /// </summary>
    public interface IEmailAdherentRepository : IRepository<EmailAdherent>
    {
        Task<IEnumerable<EmailAdherent>> GetByAdherentAsync(int adherentId);
    }
}
