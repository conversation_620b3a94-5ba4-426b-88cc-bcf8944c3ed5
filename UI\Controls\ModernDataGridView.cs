using System;
using System.Drawing;
using System.Windows.Forms;

namespace GestionAssociations.UI.Controls
{
    /// <summary>
    /// DataGridView moderne avec style personnalisé
    /// </summary>
    public class ModernDataGridView : DataGridView
    {
        public ModernDataGridView()
        {
            InitializeComponent();
            ApplyModernStyle();
        }

        private void InitializeComponent()
        {
            // Configuration de base
            SetStyle(ControlStyles.AllPaintingInWmPaint | 
                    ControlStyles.UserPaint | 
                    ControlStyles.DoubleBuffer | 
                    ControlStyles.ResizeRedraw, true);

            // Propriétés de base
            AllowUserToAddRows = false;
            AllowUserToDeleteRows = false;
            AllowUserToResizeRows = false;
            ReadOnly = true;
            SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            MultiSelect = false;
            AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            RowHeadersVisible = false;
            EnableHeadersVisualStyles = false;
        }

        private void ApplyModernStyle()
        {
            // Couleurs principales
            BackgroundColor = Color.White;
            GridColor = Color.FromArgb(230, 230, 230);
            BorderStyle = BorderStyle.None;

            // Style des cellules
            DefaultCellStyle.BackColor = Color.White;
            DefaultCellStyle.ForeColor = Color.FromArgb(64, 64, 64);
            DefaultCellStyle.SelectionBackColor = Color.FromArgb(70, 70, 100);
            DefaultCellStyle.SelectionForeColor = Color.White;
            DefaultCellStyle.Font = new Font("Segoe UI", 9F);
            DefaultCellStyle.Padding = new Padding(5, 8, 5, 8);

            // Style des lignes alternées
            AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 248, 248);
            AlternatingRowsDefaultCellStyle.ForeColor = Color.FromArgb(64, 64, 64);
            AlternatingRowsDefaultCellStyle.SelectionBackColor = Color.FromArgb(70, 70, 100);
            AlternatingRowsDefaultCellStyle.SelectionForeColor = Color.White;

            // Style de l'en-tête
            ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(70, 70, 100);
            ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
            ColumnHeadersDefaultCellStyle.Padding = new Padding(10, 8, 10, 8);
            ColumnHeadersHeight = 40;
            ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;

            // Style des bordures
            CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            RowsDefaultCellStyle.SelectionBackColor = Color.FromArgb(70, 70, 100);
            RowsDefaultCellStyle.SelectionForeColor = Color.White;

            // Hauteur des lignes
            RowTemplate.Height = 35;

            // Curseur
            Cursor = Cursors.Hand;
        }

        protected override void OnCellMouseEnter(DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(240, 240, 245);
            }
            base.OnCellMouseEnter(e);
        }

        protected override void OnCellMouseLeave(DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                if (e.RowIndex % 2 == 0)
                {
                    Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.White;
                }
                else
                {
                    Rows[e.RowIndex].DefaultCellStyle.BackColor = Color.FromArgb(248, 248, 248);
                }
            }
            base.OnCellMouseLeave(e);
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            // Dessiner une bordure subtile
            using (var pen = new Pen(Color.FromArgb(200, 200, 200), 1))
            {
                e.Graphics.DrawRectangle(pen, 0, 0, Width - 1, Height - 1);
            }
        }

        /// <summary>
        /// Ajoute une colonne avec style personnalisé
        /// </summary>
        /// <param name="name">Nom de la colonne</param>
        /// <param name="headerText">Texte d'en-tête</param>
        /// <param name="width">Largeur</param>
        /// <param name="isNumeric">Si la colonne contient des nombres</param>
        public void AddStyledColumn(string name, string headerText, int width, bool isNumeric = false)
        {
            var column = new DataGridViewTextBoxColumn
            {
                Name = name,
                HeaderText = headerText,
                Width = width,
                SortMode = DataGridViewColumnSortMode.Automatic
            };

            if (isNumeric)
            {
                column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
                column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleRight;
            }

            Columns.Add(column);
        }

        /// <summary>
        /// Ajoute une colonne d'action avec bouton
        /// </summary>
        /// <param name="name">Nom de la colonne</param>
        /// <param name="headerText">Texte d'en-tête</param>
        /// <param name="buttonText">Texte du bouton</param>
        /// <param name="width">Largeur</param>
        public void AddActionColumn(string name, string headerText, string buttonText, int width = 100)
        {
            var column = new DataGridViewButtonColumn
            {
                Name = name,
                HeaderText = headerText,
                Text = buttonText,
                UseColumnTextForButtonValue = true,
                Width = width
            };

            column.DefaultCellStyle.BackColor = Color.FromArgb(70, 70, 100);
            column.DefaultCellStyle.ForeColor = Color.White;
            column.DefaultCellStyle.SelectionBackColor = Color.FromArgb(50, 50, 80);

            Columns.Add(column);
        }

        /// <summary>
        /// Configure la pagination
        /// </summary>
        /// <param name="totalRecords">Nombre total d'enregistrements</param>
        /// <param name="pageSize">Taille de la page</param>
        /// <param name="currentPage">Page actuelle</param>
        public void SetPagination(int totalRecords, int pageSize, int currentPage)
        {
            // Cette méthode peut être étendue pour afficher des informations de pagination
            // dans un contrôle séparé ou dans le pied de page
        }
    }
}
